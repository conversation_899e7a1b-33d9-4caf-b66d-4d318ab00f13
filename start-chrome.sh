#!/bin/bash

# Start Chrome with extension loaded and remote debugging enabled
# This script starts Chrome with all the necessary flags for testing

EXTENSION_PATH="$(pwd)/tab-screen-share-extension"

echo "🚀 Starting Chrome with extension and remote debugging..."
echo "📁 Extension path: $EXTENSION_PATH"
echo "🔧 Remote debugging port: 9222"
echo ""

# Kill any existing Chrome processes to ensure clean start
pkill -f "Google Chrome" 2>/dev/null || true
sleep 2

# Start Chrome with all necessary flags
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome \
  --remote-debugging-port=9222 \
  --no-first-run \
  --no-default-browser-check \
  --auto-select-desktop-capture-source="Entire screen" \
  --use-fake-ui-for-media-stream \
  --use-fake-device-for-media-stream \
  --disable-extensions-except="$EXTENSION_PATH" \
  --load-extension="$EXTENSION_PATH" \
  --new-window \
  --user-data-dir="/tmp/chrome-test-profile" \
  &

echo "✅ Chrome started with PID: $!"
echo ""
echo "📋 Next steps:"
echo "1. Wait for Chrome to fully load (5-10 seconds)"
echo "2. Start signaling server: cd signaling-server && node server.js"
echo "3. Run test script: node simple-test.js"
echo ""
echo "🌐 Chrome DevTools endpoint: http://localhost:9222"
echo "🔧 Extension should be loaded automatically"
echo ""
echo "Press Ctrl+C to stop Chrome"

# Wait for interrupt
trap 'echo ""; echo "🛑 Stopping Chrome..."; pkill -f "Google Chrome"; exit 0' INT
wait
