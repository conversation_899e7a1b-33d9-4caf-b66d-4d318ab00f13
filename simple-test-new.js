#!/usr/bin/env node

/**
 * Simple CDP Script - Test Extension Screen Sharing
 *
 * Prerequisites:
 * 1. Start Chrome with extension loaded:
 *    ./start-chrome.sh
 *
 * 2. Start signaling server:
 *    cd signaling-server && node server.js
 *
 * 3. Run this script:
 *    node simple-test-new.js
 */

import { CDP } from "./simple-cdp.js";
import puppeteer from "puppeteer";
import WebSocket from "ws";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function simpleTest() {
  console.log("🧪 Starting simple CDP extension test...");

  let browser = null;
  let sessionId = null;

  try {
    const extensionPath = path.join(__dirname, "tab-screen-share-extension");
    // Step 1: Launch Chrome with extension using Puppeteer
    console.log("🚀 Launching Chrome with extension...");
    const browser = await puppeteer.launch({
      headless: false,
      devtools: false,
      args: [
        `--disable-extensions-except=${extensionPath}`,
        `--load-extension=${extensionPath}`,
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--auto-accept-this-tab-capture",
        "--remote-debugging-port=9222",
      ],
      defaultViewport: null,
    });

    console.log("✅ Chrome launched with extension");
    await new Promise((resolve) => setTimeout(resolve, 3000));
    const testPageUrl = `https://google.com`;

    // Get the correct WebSocket endpoint from Chrome
    const versionResponse = await fetch("http://localhost:9222/json/version");
    const versionData = await versionResponse.json();
    const wsEndpoint = versionData.webSocketDebuggerUrl;

    console.log("🔗 Connecting to:", wsEndpoint);
    const cdpInstance = new CDP({ webSocketDebuggerUrl: wsEndpoint });

    // For remote browsers, we need to attach to a target and get sessionId
    console.log("📋 Getting list of targets...");
    const targets = await cdpInstance.Target.getTargets();
    console.log("🎯 Available targets:", targets.targetInfos.length);

    // Find a page target or create one
    let pageTarget = targets.targetInfos.find(
      (target) => target.type === "page"
    );

    if (!pageTarget) {
      console.log("📍 Creating new page target...");
      const createResult = await cdpInstance.Target.createTarget({
        url: testPageUrl,
      });
      pageTarget = { targetId: createResult.targetId };
    }

    console.log("📍 Found/created page target:", pageTarget.targetId);

    // Attach to the target
    const attachResult = await cdpInstance.Target.attachToTarget({
      targetId: pageTarget.targetId,
      flatten: true,
    });

    sessionId = attachResult.sessionId;
    console.log("✅ Attached to target with sessionId:", sessionId);

    // Enable necessary domains (with sessionId for remote browsers)
    await cdpInstance.Runtime.enable(null, sessionId);
    console.log("✅ Runtime domain enabled");

    await cdpInstance.Page.enable(null, sessionId);
    console.log("✅ Page domain enabled");

    // Navigate to our test page
    console.log(`📍 Navigating to: ${testPageUrl}`);
    await cdpInstance.Page.navigate({ url: testPageUrl }, sessionId);

    // Wait for page to load
    console.log("⏳ Waiting for page to load...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Get page title to confirm we're on the right page
    const titleResult = await cdpInstance.Runtime.evaluate(
      {
        expression: "document.title",
        returnByValue: true,
      },
      sessionId
    );
    console.log("📄 Page title:", titleResult.result.value);

    // Step 3: Find extension using CDP target detection
    console.log("🔍 Finding extension...");

    // Wait a bit more for extension to fully load
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const allTargets = await cdpInstance.Target.getTargets();
    const extensionTargets = allTargets.targetInfos.filter(
      (target) => target.url && target.url.startsWith("chrome-extension://")
    );

    console.log(`📋 Found ${extensionTargets.length} extension targets`);

    let extensionId = null;
    for (const target of extensionTargets) {
      const url = target.url;
      const match = url.match(/chrome-extension:\/\/([a-z]+)/);
      if (match) {
        const potentialId = match[1];

        // Test if this extension has our popup using CDP
        try {
          const popupUrl = `chrome-extension://${potentialId}/popup.html`;
          console.log(`🔍 Testing extension: ${potentialId}`);

          // Create popup target using CDP
          const popupCreateResult = await cdpInstance.Target.createTarget({
            url: popupUrl,
          });

          const popupAttachResult = await cdpInstance.Target.attachToTarget({
            targetId: popupCreateResult.targetId,
            flatten: true,
          });

          const popupSessionId = popupAttachResult.sessionId;

          // Enable domains for popup
          await cdpInstance.Runtime.enable({}, popupSessionId);
          await cdpInstance.Page.enable({}, popupSessionId);

          // Wait for popup to load
          await new Promise((resolve) => setTimeout(resolve, 2000));

          // Check if popup has our button
          const hasButton = await cdpInstance.Runtime.evaluate(
            {
              expression: 'document.querySelector("#startCaptureBtn") !== null',
              returnByValue: true,
            },
            popupSessionId
          );

          if (hasButton.result.value) {
            extensionId = potentialId;
            console.log(`🔧 Found extension: ${extensionId}`);

            // Step 4: Create test tab and interact with extension
            console.log("🎬 Starting screen capture automation...");

            // Create test tab using CDP with a simple web page
            const testTabResult = await cdpInstance.Target.createTarget({
              url: "https://example.com",
            });

            const testTabAttachResult = await cdpInstance.Target.attachToTarget(
              {
                targetId: testTabResult.targetId,
                flatten: true,
              }
            );

            const testTabSessionId = testTabAttachResult.sessionId;
            await cdpInstance.Runtime.enable({}, testTabSessionId);
            await cdpInstance.Page.enable({}, testTabSessionId);

            // Wait for test tab to load
            await new Promise((resolve) => setTimeout(resolve, 2000));

            // Activate the test tab to make it the current active tab
            await cdpInstance.Target.activateTarget({
              targetId: testTabResult.targetId,
            });
            await new Promise((resolve) => setTimeout(resolve, 1000));
            console.log("📍 Test tab created and activated");

            // IMPORTANT: Trigger extension using keyboard shortcut
            console.log(
              "🔧 Triggering extension via keyboard shortcut (Ctrl+Shift+U)..."
            );

            // Send Ctrl+Shift+U keyboard shortcut (no need to enable Input domain)
            // Modifiers: 2 = Ctrl, 4 = Shift, so 6 = Ctrl+Shift
            // Press Cmd + Shift
            cdpInstance.Input.dispatchKeyEvent(
              {
                type: "rawKeyDown",
                modifiers: 12, // Meta(4) + Shift(8)
                key: "Meta",
                code: "MetaLeft",
              },
              testTabSessionId
            );
            cdpInstance.Input.dispatchKeyEvent(
              {
                type: "rawKeyDown",
                modifiers: 12,
                key: "Shift",
                code: "ShiftLeft",
              },
              testTabSessionId
            );

            // Press and release U
            cdpInstance.Input.dispatchKeyEvent(
              {
                type: "keyDown",
                modifiers: 12,
                key: "U",
                code: "KeyU",
                text: "U",
              },
              testTabSessionId
            );
            cdpInstance.Input.dispatchKeyEvent(
              {
                type: "keyUp",
                modifiers: 12,
                key: "U",
                code: "KeyU",
                text: "U",
              },
              testTabSessionId
            );

            // Release Cmd + Shift
            cdpInstance.Input.dispatchKeyEvent(
              {
                type: "keyUp",
                modifiers: 0,
                key: "Shift",
                code: "ShiftLeft",
              },
              testTabSessionId
            );
            cdpInstance.Input.dispatchKeyEvent(
              {
                type: "keyUp",
                modifiers: 0,
                key: "Meta",
                code: "MetaLeft",
              },
              testTabSessionId
            );

            await new Promise((resolve) => setTimeout(resolve, 2000));
            console.log("✅ Extension shortcut triggered");

            // Click start capture button in popup
            await cdpInstance.Runtime.evaluate(
              {
                expression:
                  'document.querySelector("#startCaptureBtn").click()',
                returnByValue: true,
              },
              popupSessionId
            );
            console.log("✅ Clicked start capture");

            // Wait to see results
            await new Promise((resolve) => setTimeout(resolve, 5000));

            // Step 5: Open web client using CDP
            console.log("🌐 Opening web client...");
            const clientResult = await cdpInstance.Target.createTarget({
              url: "http://localhost:3000",
            });

            const clientAttachResult = await cdpInstance.Target.attachToTarget({
              targetId: clientResult.targetId,
              flatten: true,
            });

            const clientSessionId = clientAttachResult.sessionId;
            await cdpInstance.Runtime.enable({}, clientSessionId);
            await cdpInstance.Page.enable({}, clientSessionId);

            // Wait for client to load and connect
            await new Promise((resolve) => setTimeout(resolve, 3000));

            // Check how many video elements are displayed
            const videoCountResult = await cdpInstance.Runtime.evaluate(
              {
                expression: 'document.querySelectorAll("video").length',
                returnByValue: true,
              },
              clientSessionId
            );

            const videoCount = videoCountResult.result.value;

            console.log(`� Found ${videoCount} video stream(s) in web client`);

            if (videoCount > 0) {
              console.log("� SUCCESS! Stream(s) are displaying in web client");
            } else {
              console.log(
                "⚠️  No streams visible yet - check signaling server logs"
              );
            }

            // Close popup target
            // await cdpInstance.Target.closeTarget({
            //   targetId: popupCreateResult.targetId,
            // });
            break;
          } else {
            // Close popup target if it's not our extension
            // await cdpInstance.Target.closeTarget({
            //   targetId: popupCreateResult.targetId,
            // });
          }
        } catch (e) {
          console.log(`⚠️ Failed to test extension ${potentialId}:`, e.message);
          continue;
        }
      }
    }

    if (!extensionId) {
      console.error("❌ Extension not found");
      console.error("   Make sure the extension is properly loaded");
      return;
    }

    console.log("🎯 Demo complete!");
    console.log("   Check the signaling server logs for stream notifications.");
    console.log("   The browser will stay open for manual inspection.");

    // Keep browser open for inspection
    console.log("   Press Ctrl+C to close the browser and exit.");
    process.on("SIGINT", async () => {
      console.log("\n👋 Closing browser...");
      if (browser) {
        await browser.close();
      }
      process.exit(0);
    });

    // Keep alive
    await new Promise(() => {});
  } catch (error) {
    console.error("❌ Error during extension demo:", error);
    if (browser) {
      await browser.close();
    }
  }
}

simpleTest().catch(console.error);
