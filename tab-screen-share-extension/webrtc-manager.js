// WebRTC Manager for Tab Screen Share Extension
class WebRTCManager {
  constructor() {
    this.peerConnections = new Map(); // streamId -> RTCPeerConnection
    this.localStreams = new Map(); // streamId -> MediaStream
    this.streamMetadata = new Map(); // streamId -> { tabId, streamInfo }
    this.signalingChannel = null;
    this.clientId = null;
    this.currentRoom = "default-room";
    this.isConnected = false;
    this.configuration = {
      iceServers: [
        { urls: "stun:stun.l.google.com:19302" },
        { urls: "stun:stun1.l.google.com:19302" },
      ],
    };
  }

  // Create a new peer connection for a specific stream
  async createPeerConnection(streamId, tabId) {
    try {
      const pc = new RTCPeerConnection(this.configuration);

      // Set up event handlers
      pc.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendSignalingMessage({
            type: "ice-candidate",
            candidate: event.candidate,
            streamId: streamId,
            tabId: tabId,
          });
        }
      };

      pc.onconnectionstatechange = () => {
        console.log(
          `Connection state for stream ${streamId} (tab ${tabId}):`,
          pc.connectionState
        );
        if (pc.connectionState === "failed") {
          this.handleConnectionFailure(streamId);
        }
      };

      pc.ontrack = (event) => {
        console.log("Received remote track for stream:", streamId);
        // Handle incoming tracks if this becomes a bidirectional connection
      };

      // Store the peer connection and metadata
      this.peerConnections.set(streamId, pc);
      this.streamMetadata.set(streamId, { tabId, streamInfo: null });

      console.log(
        `Peer connection created for stream ${streamId} (tab ${tabId})`
      );
      return pc;
    } catch (error) {
      console.error("Failed to create peer connection:", error);
      throw error;
    }
  }

  // Add a media stream to the peer connection
  async addStreamToPeer(streamId, stream) {
    const pc = this.peerConnections.get(streamId);
    if (!pc) {
      throw new Error(`No peer connection found for stream ${streamId}`);
    }

    const metadata = this.streamMetadata.get(streamId);
    if (!metadata) {
      throw new Error(`No metadata found for stream ${streamId}`);
    }

    try {
      // Add all tracks from the stream
      stream.getTracks().forEach((track) => {
        pc.addTrack(track, stream);
        console.log(
          `Added ${track.kind} track to peer connection for stream ${streamId} (tab ${metadata.tabId})`
        );
      });

      // Store the stream reference
      this.localStreams.set(streamId, stream);

      // Update metadata with stream info
      metadata.streamInfo = {
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length,
      };

      // Notify that stream is available
      this.notifyStreamAvailable({
        streamId: streamId,
        tabId: metadata.tabId,
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length,
      });

      return true;
    } catch (error) {
      console.error("Failed to add stream to peer connection:", error);
      throw error;
    }
  }

  // Create and send an offer
  async createOffer(tabId) {
    const pc = this.peerConnections.get(tabId);
    if (!pc) {
      throw new Error(`No peer connection found for tab ${tabId}`);
    }

    try {
      const offer = await pc.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true,
      });

      await pc.setLocalDescription(offer);

      // Send offer through signaling channel
      this.sendSignalingMessage({
        type: "offer",
        sdp: offer,
        tabId: tabId,
      });

      console.log(`Offer created and sent for tab ${tabId}`);
      return offer;
    } catch (error) {
      console.error("Failed to create offer:", error);
      throw error;
    }
  }

  // Handle incoming answer
  async handleAnswer(tabId, answer) {
    const pc = this.peerConnections.get(tabId);
    if (!pc) {
      throw new Error(`No peer connection found for tab ${tabId}`);
    }

    try {
      await pc.setRemoteDescription(answer);
      console.log(`Answer processed for tab ${tabId}`);
    } catch (error) {
      console.error("Failed to handle answer:", error);
      throw error;
    }
  }

  // Handle incoming ICE candidate
  async handleIceCandidate(tabId, candidate) {
    const pc = this.peerConnections.get(tabId);
    if (!pc) {
      console.warn(
        `No peer connection found for tab ${tabId} when handling ICE candidate`
      );
      return;
    }

    try {
      await pc.addIceCandidate(candidate);
      console.log(`ICE candidate added for tab ${tabId}`);
    } catch (error) {
      console.error("Failed to add ICE candidate:", error);
    }
  }

  // Close peer connection for a specific tab
  async closePeerConnection(tabId) {
    const pc = this.peerConnections.get(tabId);
    const stream = this.localStreams.get(tabId);

    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
      this.localStreams.delete(tabId);
    }

    if (pc) {
      pc.close();
      this.peerConnections.delete(tabId);
      console.log(`Peer connection closed for tab ${tabId}`);
    }
  }

  // Handle connection failure
  handleConnectionFailure(tabId) {
    console.warn(
      `Connection failed for tab ${tabId}, attempting to reconnect...`
    );
    // Could implement reconnection logic here
    this.sendSignalingMessage({
      type: "connection-failed",
      tabId: tabId,
    });
  }

  // Send signaling message
  sendSignalingMessage(message) {
    if (
      this.signalingChannel &&
      this.signalingChannel.readyState === WebSocket.OPEN
    ) {
      this.signalingChannel.send(JSON.stringify(message));
      console.log("Signaling message sent:", message.type);
    } else {
      console.warn(
        "No signaling channel available. Message not sent:",
        message
      );
    }
  }

  // Join a room for signaling
  joinRoom(roomId = "default-room") {
    this.currentRoom = roomId;
    this.sendSignalingMessage({
      type: "join-room",
      roomId: roomId,
    });
  }

  // Notify that stream is available
  notifyStreamAvailable(streamInfo) {
    this.sendSignalingMessage({
      type: "start-stream",
      streamId: streamInfo.streamId,
      tabId: streamInfo.tabId,
      streamInfo: streamInfo,
    });
  }

  // Notify that stream stopped
  notifyStreamStopped(streamId, tabId) {
    this.sendSignalingMessage({
      type: "stop-stream",
      streamId: streamId,
      tabId: tabId,
    });
  }

  // Set up signaling channel
  setupSignalingChannel(url = "ws://localhost:3001") {
    try {
      console.log("Connecting to signaling server:", url);
      this.signalingChannel = new WebSocket(url);

      this.signalingChannel.onopen = () => {
        console.log("Signaling channel connected");
        this.isConnected = true;

        // Register as extension
        this.sendSignalingMessage({
          type: "register",
          clientType: "extension",
        });
      };

      this.signalingChannel.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleSignalingMessage(message);
        } catch (error) {
          console.error("Invalid signaling message:", error);
        }
      };

      this.signalingChannel.onerror = (error) => {
        console.error("Signaling channel error:", error);
        this.isConnected = false;
      };

      this.signalingChannel.onclose = () => {
        console.log("Signaling channel closed");
        this.isConnected = false;
      };
    } catch (error) {
      console.error("Failed to setup signaling channel:", error);
    }
  }

  // Handle incoming signaling messages
  handleSignalingMessage(message) {
    console.log("Received signaling message:", message.type);

    switch (message.type) {
      case "welcome":
        this.clientId = message.clientId;
        console.log("Assigned client ID:", this.clientId);
        break;

      case "registered":
        console.log("Registered as extension");
        // Auto-join default room
        this.joinRoom(this.currentRoom);
        break;

      case "room-joined":
        console.log("Joined room:", message.roomId);
        this.currentRoom = message.roomId;
        break;

      case "client-joined":
        if (message.clientType === "client") {
          console.log("Web client joined room:", message.clientId);
          // If we have an active stream, create offer
          this.handleClientJoined(message.clientId);
        }
        break;

      case "answer":
        this.handleAnswer(message.fromClientId, message.answer);
        break;

      case "ice-candidate":
        this.handleIceCandidate(message.fromClientId, message.candidate);
        break;

      default:
        console.warn("Unknown signaling message type:", message.type);
    }
  }

  // Handle when a web client joins the room
  async handleClientJoined(clientId) {
    // Check if we have any active streams to offer
    for (const [tabId, stream] of this.localStreams) {
      const pc = this.peerConnections.get(tabId);
      if (pc && stream) {
        try {
          // Create offer for this client
          const offer = await pc.createOffer();
          await pc.setLocalDescription(offer);

          this.sendSignalingMessage({
            type: "offer",
            offer: offer,
            targetClientId: clientId,
          });

          console.log("Sent offer to client:", clientId);
        } catch (error) {
          console.error("Failed to create offer:", error);
        }
      }
    }
  }

  // Get connection stats
  async getConnectionStats(tabId) {
    const pc = this.peerConnections.get(tabId);
    if (!pc) {
      return null;
    }

    try {
      const stats = await pc.getStats();
      return stats;
    } catch (error) {
      console.error("Failed to get connection stats:", error);
      return null;
    }
  }

  // Clean up all connections
  cleanup() {
    for (const [tabId] of this.peerConnections) {
      this.closePeerConnection(tabId);
    }

    if (this.signalingChannel) {
      this.signalingChannel.close();
      this.signalingChannel = null;
    }

    console.log("WebRTC manager cleaned up");
  }
}

// Export for use in other scripts
if (typeof module !== "undefined" && module.exports) {
  module.exports = WebRTCManager;
} else if (typeof window !== "undefined") {
  window.WebRTCManager = WebRTCManager;
}
