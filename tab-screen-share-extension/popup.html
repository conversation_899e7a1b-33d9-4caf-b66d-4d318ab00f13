<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tab Screen Share</title>
    <link rel="stylesheet" href="popup.css" />
  </head>
  <body>
    <div class="container">
      <header class="header">
        <h1>Tab Screen Share</h1>
        <div class="status-indicator" id="captureStatus">
          <span class="status-dot"></span>
          <span class="status-text">Not Capturing</span>
        </div>
      </header>

      <main class="main-content">
        <!-- Current Tab Information -->
        <section class="tab-info">
          <h3>Current Tab</h3>
          <div class="tab-details" id="tabDetails">
            <p class="tab-url" id="tabUrl">Loading...</p>
            <p class="tab-title" id="tabTitle">Loading...</p>
          </div>
        </section>

        <!-- Video Preview -->
        <section class="video-preview">
          <h3>Capture Preview</h3>
          <div class="video-container">
            <video id="previewVideo" muted autoplay playsinline>
              <p>Video preview not available</p>
            </video>
            <div class="video-overlay" id="videoOverlay">
              <p>Click "Start Capture" to begin capturing this tab</p>
            </div>
          </div>
        </section>

        <!-- Controls -->
        <section class="controls">
          <div class="control-group">
            <button id="startCaptureBtn" class="btn btn-primary">
              Start Capture
            </button>
            <button id="stopCaptureBtn" class="btn btn-secondary" disabled>
              Stop Capture
            </button>
          </div>
        </section>

        <!-- Capture Information -->
        <section class="capture-info">
          <h3>Capture Information</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>Status:</label>
              <span id="captureStatusText">Not capturing</span>
            </div>
            <div class="info-item">
              <label>Duration:</label>
              <span id="captureDuration">-</span>
            </div>
            <div class="info-item">
              <label>Audio Tracks:</label>
              <span id="audioTracks">-</span>
            </div>
            <div class="info-item">
              <label>Video Tracks:</label>
              <span id="videoTracks">-</span>
            </div>
            <div class="info-item">
              <label>WebRTC Status:</label>
              <span id="webrtcStatus">Disconnected</span>
            </div>
            <div class="info-item">
              <label>Signaling:</label>
              <span id="signalingStatus">Disconnected</span>
            </div>
          </div>
        </section>

        <!-- Activity Log -->
        <section class="logs">
          <h3>Activity Log</h3>
          <div class="log-container" id="logContainer">
            <p class="log-entry">Extension loaded</p>
          </div>
        </section>
      </main>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
