// Popup JavaScript for Tab Screen Share Extension

// Storage utility for persistent state management
class PopupStorage {
  static async get(key, defaultValue = null) {
    try {
      const result = await chrome.storage.local.get([key]);
      return result[key] !== undefined ? result[key] : defaultValue;
    } catch (error) {
      console.error("Storage get error:", error);
      return defaultValue;
    }
  }

  static async set(key, value) {
    try {
      await chrome.storage.local.set({ [key]: value });
    } catch (error) {
      console.error("Storage set error:", error);
    }
  }

  static async getTabState(tabId) {
    const tabStates = await this.get("tabStates", {});
    return (
      tabStates[tabId] || {
        isCapturing: false,
        isWebRTCStreaming: false,
        captureStartTime: null,
        streamId: null,
      }
    );
  }

  static async setTabState(tabId, state) {
    const tabStates = await this.get("tabStates", {});
    tabStates[tabId] = { ...tabStates[tabId], ...state };
    await this.set("tabStates", tabStates);
  }

  static async clearTabState(tabId) {
    const tabStates = await this.get("tabStates", {});
    delete tabStates[tabId];
    await this.set("tabStates", tabStates);
  }
}

class TabScreenSharePopup {
  constructor() {
    this.currentTab = null;
    this.isCapturing = false;
    this.isWebRTCStreaming = false;
    this.captureStartTime = null;
    this.updateInterval = null;
    this.previewVideo = null;
    this.currentStream = null;
    this.setupEventListeners();
    this.initialize();
  }

  setupEventListeners() {
    // Button event listeners
    document.getElementById("startCaptureBtn").addEventListener("click", () => {
      this.startCapture();
    });

    document.getElementById("stopCaptureBtn").addEventListener("click", () => {
      this.stopCapture();
    });
  }

  async initialize() {
    try {
      // Get video element reference
      this.previewVideo = document.getElementById("previewVideo");

      // Get current tab information
      await this.loadCurrentTab();

      // Load persistent state for current tab
      if (this.currentTab) {
        await this.loadTabState();
      }

      // Check if already capturing
      await this.checkCaptureStatus();

      // Update UI
      this.updateUI();

      this.log("Extension popup initialized");
    } catch (error) {
      this.log(`Initialization error: ${error.message}`, "error");
    }
  }

  async loadTabState() {
    if (!this.currentTab) return;

    try {
      const state = await PopupStorage.getTabState(this.currentTab.id);
      this.isCapturing = state.isCapturing;
      this.isWebRTCStreaming = state.isWebRTCStreaming;
      this.captureStartTime = state.captureStartTime;

      this.log(
        `Loaded state for tab ${this.currentTab.id}: capturing=${this.isCapturing}, webrtc=${this.isWebRTCStreaming}`
      );
    } catch (error) {
      this.log(`Error loading tab state: ${error.message}`, "error");
    }
  }

  async saveTabState() {
    if (!this.currentTab) return;

    try {
      await PopupStorage.setTabState(this.currentTab.id, {
        isCapturing: this.isCapturing,
        isWebRTCStreaming: this.isWebRTCStreaming,
        captureStartTime: this.captureStartTime,
      });
    } catch (error) {
      this.log(`Error saving tab state: ${error.message}`, "error");
    }
  }

  async loadCurrentTab() {
    try {
      const response = await this.sendMessage({ action: "getActiveTab" });
      if (response.success) {
        this.currentTab = response.data;
        this.updateTabInfo();
      }
    } catch (error) {
      this.log(`Failed to load tab info: ${error.message}`, "error");
    }
  }

  async checkCaptureStatus() {
    if (!this.currentTab) return;

    try {
      const response = await this.sendMessage({
        action: "getCaptureStatus",
        tabId: this.currentTab.id,
      });

      if (response.success && response.data.isCapturing) {
        this.isCapturing = true;
        this.captureStartTime = response.data.startTime;
        this.updateCaptureInfo(response.data);
        this.startPeriodicUpdates();
      }
    } catch (error) {
      this.log(`Failed to check capture status: ${error.message}`, "error");
    }
  }

  updateTabInfo() {
    if (this.currentTab) {
      document.getElementById("tabUrl").textContent = this.currentTab.url;
      document.getElementById("tabTitle").textContent = this.currentTab.title;
    }
  }

  async startCapture() {
    if (!this.currentTab) {
      this.log("No active tab found", "error");
      return;
    }

    try {
      this.log("Starting tab capture...");

      const response = await this.sendMessage({
        action: "startCapture",
        tabId: this.currentTab.id,
      });
      console.log("Start capture response:", response);
      if (response.success) {
        this.isCapturing = true;
        this.captureStartTime = response.data.startTime;

        // Save state to storage
        await this.saveTabState();

        this.updateUI();

        // Convert captureInfo to the format expected by updateCaptureInfo
        const captureStatus = {
          isCapturing: true, // We just started, so it's capturing
          method: response.data.method,
          audioTracks: response.data.audioTracks,
          videoTracks: response.data.videoTracks,
          duration: 0,
        };
        this.updateCaptureInfo(captureStatus);
        this.startPeriodicUpdates();

        // Try to get preview stream
        await this.setupVideoPreview(response.data.streamId);

        // Wait a moment and then check status again to ensure it's properly updated
        setTimeout(async () => {
          await this.checkCaptureStatus();
        }, 1000);

        this.log("Tab capture started successfully", "success");
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to start capture: ${error.message}`, "error");
    }
  }

  async stopCapture() {
    if (!this.currentTab) return;

    try {
      this.log("Stopping tab capture...");

      const response = await this.sendMessage({
        action: "stopCapture",
        tabId: this.currentTab.id,
      });

      if (response.success) {
        this.isCapturing = false;
        this.isWebRTCStreaming = false; // Stop WebRTC when capture stops
        this.captureStartTime = null;

        // Save state to storage
        await this.saveTabState();

        this.stopPeriodicUpdates();
        this.updateUI();
        this.clearCaptureInfo();
        this.clearVideoPreview();
        this.updateWebRTCStatus("Disconnected");

        this.log("Tab capture stopped", "success");
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      this.log(`Failed to stop capture: ${error.message}`, "error");
    }
  }

  updateUI() {
    // Update status indicator
    const statusIndicator = document.getElementById("captureStatus");
    const statusDot = statusIndicator.querySelector(".status-dot");
    const statusText = statusIndicator.querySelector(".status-text");

    if (this.isCapturing) {
      statusDot.classList.add("capturing");
      statusText.textContent = "Capturing";
    } else {
      statusDot.classList.remove("capturing");
      statusText.textContent = "Not Capturing";
    }

    // Update video overlay
    const videoOverlay = document.getElementById("videoOverlay");
    if (this.isCapturing) {
      videoOverlay.classList.add("hidden");
    } else {
      videoOverlay.classList.remove("hidden");
    }
  }

  updateCaptureInfo(data) {
    const statusText = data.isCapturing ? "Active" : "Inactive";
    const method = data.method ? ` (${data.method})` : "";
    document.getElementById("captureStatusText").textContent =
      statusText + method;

    document.getElementById("audioTracks").textContent = data.audioTracks || 0;
    document.getElementById("videoTracks").textContent = data.videoTracks || 0;

    if (data.duration) {
      this.updateDuration(data.duration);
    }
  }

  clearCaptureInfo() {
    document.getElementById("captureStatusText").textContent = "Not capturing";
    document.getElementById("captureDuration").textContent = "-";
    document.getElementById("audioTracks").textContent = "-";
    document.getElementById("videoTracks").textContent = "-";
  }

  updateDuration(duration) {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    const formattedDuration = `${minutes}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
    document.getElementById("captureDuration").textContent = formattedDuration;
  }

  startPeriodicUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    this.updateInterval = setInterval(async () => {
      if (this.isCapturing && this.currentTab) {
        try {
          const response = await this.sendMessage({
            action: "getCaptureStatus",
            tabId: this.currentTab.id,
          });

          if (response.success) {
            if (response.data.isCapturing) {
              this.updateCaptureInfo(response.data);
            } else {
              // Capture stopped externally
              this.isCapturing = false;
              this.isWebRTCStreaming = false; // Stop WebRTC when capture stops externally
              this.captureStartTime = null;

              // Save state to storage
              await this.saveTabState();

              this.stopPeriodicUpdates();
              this.updateUI();
              this.clearCaptureInfo();
              this.updateWebRTCStatus("Disconnected");
              this.log("Capture stopped externally", "info");
            }
          }
        } catch (error) {
          // Silently handle errors in periodic updates
        }
      }
    }, 1000); // Update every second
  }

  stopPeriodicUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  sendMessage(message) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
  }

  async setupVideoPreview(streamId) {
    try {
      // For popup preview, we need to create a new getUserMedia call
      // since we can't directly access the stream from offscreen document
      if (!streamId) {
        this.log("No stream ID available for preview", "warning");
        return;
      }

      this.log("Setting up video preview with streamId: " + streamId);

      // Check if getUserMedia is available in popup context
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        this.log("getUserMedia not available in popup context", "warning");
        return;
      }

      // Create a new stream for preview in the popup context
      const previewStream = await navigator.mediaDevices.getUserMedia({
        audio: false, // Don't capture audio for preview to avoid feedback
        video: {
          mandatory: {
            chromeMediaSource: "tab",
            chromeMediaSourceId: streamId,
          },
        },
      });

      if (this.previewVideo && previewStream) {
        this.currentStream = previewStream;
        this.previewVideo.srcObject = previewStream;

        // Add event listeners for video
        this.previewVideo.onloadedmetadata = () => {
          this.previewVideo.play().catch((e) => {
            this.log("Failed to play video: " + e.message, "warning");
          });
        };

        this.log("Video preview started", "success");
      }
    } catch (error) {
      this.log(`Failed to setup video preview: ${error.message}`, "warning");
      // Preview failure shouldn't stop the main capture

      // Try alternative approach - request preview from background
      this.log("Attempting alternative preview method...", "info");
      this.requestPreviewFromBackground(streamId);
    }
  }

  async requestPreviewFromBackground(streamId) {
    try {
      // This is a placeholder for getting preview from background script
      // In a real implementation, you might send the stream through a different mechanism
      this.log("Preview from background not implemented yet", "info");
    } catch (error) {
      this.log(
        "Alternative preview method failed: " + error.message,
        "warning"
      );
    }
  }

  updateWebRTCStatus(status) {
    const statusElement = document.getElementById("webrtcStatus");
    if (statusElement) {
      statusElement.textContent = status;
    }
  }

  clearVideoPreview() {
    if (this.currentStream) {
      this.currentStream.getTracks().forEach((track) => track.stop());
      this.currentStream = null;
    }

    if (this.previewVideo) {
      this.previewVideo.srcObject = null;
    }

    this.log("Video preview cleared");
  }

  log(message, type = "info") {
    const logContainer = document.getElementById("logContainer");
    const logEntry = document.createElement("p");
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;

    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;

    // Keep only last 20 log entries
    while (logContainer.children.length > 20) {
      logContainer.removeChild(logContainer.firstChild);
    }

    console.log(`[${type.toUpperCase()}] ${message}`);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  const popup = new TabScreenSharePopup();

  // Clean up video preview when popup is closed
  window.addEventListener("beforeunload", () => {
    popup.clearVideoPreview();
  });
});
