# Tab Screen Share Automation Script

This Node.js script uses <PERSON><PERSON><PERSON><PERSON> to automate testing of the multi-stream functionality in the Tab Screen Share extension.

## What it does

1. **Launches Chrome** with the tab-screen-share-extension loaded
2. **Creates multiple test tabs** (default: 3) with animated content
3. **Automatically starts screen sharing** for each tab using the extension
4. **Opens the web client** and connects to the signaling server
5. **Verifies** that all streams appear in the web client grid

## Prerequisites

1. **Signaling server running** on port 3001:
   ```bash
   cd signaling-server
   npm install
   node server.js
   ```

2. **Web client running** on port 3000:
   ```bash
   cd signaling-server
   # Open another terminal
   npx http-server public -p 3000
   ```

## Usage

### Quick Start
```bash
npm run test-automation
```

### Manual Run
```bash
node automation-script.js
```

## What to expect

The script will:

1. **Launch Chrome** with the extension loaded (browser stays visible)
2. **Create 3 test tabs** with animated content
3. **Automatically trigger** the extension popup and start capture/streaming for each tab
4. **Open the web client** and connect to the signaling server
5. **Display results** showing how many streams were created vs. displayed

### Success Output
```
📊 Web client is displaying 3 stream(s)
🎉 SUCCESS: All streams are displayed correctly!

📋 Test Results:
  • Test tabs created: 3
  • Streams started: 3
  • Streams displayed in web client: 3
  • Test status: PASSED ✅
```

### If the fix worked
You should see all 3 video streams in the web client grid layout, each showing different animated content from the test tabs.

### If there's still an issue
The script will show a mismatch between expected and actual stream counts, helping identify remaining problems.

## Configuration

You can modify the script behavior by editing the `config` object in `automation-script.js`:

```javascript
this.config = {
  numberOfStreams: 3,        // Number of test tabs/streams
  delayBetweenActions: 2000, // Delay between starting each stream (ms)
  // ... other settings
};
```

## Troubleshooting

### Extension not found
- Make sure the extension is in the `tab-screen-share-extension` directory
- Check that `manifest.json` exists and is valid

### Signaling server connection failed
- Ensure the signaling server is running on port 3001
- Check that no firewall is blocking the connection

### Web client not loading
- Make sure the web client is served on port 3000
- Try accessing `http://localhost:3000` manually first

### Streams not appearing
- Check the browser console for WebRTC errors
- Verify the extension fix was applied correctly
- Look at the signaling server logs for offer/answer messages

## Manual Testing

After the automation completes, the browser stays open so you can:

1. **Inspect the extension popup** on each tab
2. **Check the web client** for all streams
3. **View browser console logs** for debugging
4. **Test manual interactions** with the streams

Press `Ctrl+C` to close the browser and exit the script.

## Files Created/Modified

- `automation-script.js` - Main automation script
- `package.json` - Added `test-automation` script
- `AUTOMATION_README.md` - This documentation

The script uses the existing test page at `tab-screen-share-extension/test-page.html` for visual content.
