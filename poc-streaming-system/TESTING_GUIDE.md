# POC Streaming System - Testing Guide

This guide will help you test the POC Browser Streaming System step by step, even if you encounter dependency or permission issues.

## 🚀 Quick Start Testing

### Option 1: Simple Test (Recommended)

Run the simple test script that doesn't require additional dependencies:

```bash
node poc-streaming-system/simple-test.js
```

This will test each component individually and provide detailed feedback.

### Option 2: Full Demo (If dependencies work)

```bash
npm run poc-demo
```

### Option 3: Manual Step-by-Step Testing

Follow the manual testing steps below if the automated tests don't work.

## 🔧 Prerequisites Check

Before testing, ensure you have:

1. **Node.js** (v16 or higher)
   ```bash
   node --version
   ```

2. **Chrome Browser** installed
   - macOS: `/Applications/Google Chrome.app/Contents/MacOS/Google Chrome`
   - Windows: Usually in Program Files
   - Linux: `google-chrome` or `chromium-browser`

3. **Basic Dependencies** (should already be installed)
   ```bash
   # Check if ws is available
   node -e "console.log(require('ws'))"
   ```

## 📋 Manual Testing Steps

### Step 1: Test Browser Manager

Create a test file `test-browser.js`:

```javascript
import { BrowserManager } from './poc-streaming-system/browser-manager.js';

async function testBrowser() {
  console.log('Testing Browser Manager...');
  
  const browserManager = new BrowserManager({
    port: 9222,
    headless: false
  });
  
  try {
    await browserManager.launchBrowser();
    console.log('✅ Chrome launched successfully');
    
    await browserManager.createControlTab();
    console.log('✅ Control tab created');
    
    const targetTab = await browserManager.createTargetTab('https://example.com');
    console.log('✅ Target tab created:', targetTab.id);
    
    // Keep running for inspection
    console.log('Browser is running. Press Ctrl+C to stop.');
    process.on('SIGINT', async () => {
      await browserManager.cleanup();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Browser test failed:', error);
    await browserManager.cleanup();
  }
}

testBrowser();
```

Run it:
```bash
node test-browser.js
```

**Expected Results:**
- Chrome browser opens with CDP enabled
- You should see a control tab (about:blank)
- You should see a target tab loading example.com
- Check `http://localhost:9222/json` to see CDP targets

### Step 2: Test Signaling Server

Create a test file `test-signaling.js`:

```javascript
import { SignalingServer } from './poc-streaming-system/signaling-server.js';
import WebSocket from 'ws';

async function testSignaling() {
  console.log('Testing Signaling Server...');
  
  const server = new SignalingServer({ port: 8080 });
  
  try {
    await server.start();
    console.log('✅ Signaling server started');
    
    // Test WebSocket connection
    const ws = new WebSocket('ws://localhost:8080');
    
    ws.on('open', () => {
      console.log('✅ WebSocket connected');
      
      ws.send(JSON.stringify({
        type: 'register-web-client',
        metadata: { test: true }
      }));
    });
    
    ws.on('message', (data) => {
      const message = JSON.parse(data.toString());
      console.log('📨 Received:', message.type);
      
      if (message.type === 'welcome') {
        console.log('✅ Welcome message received');
        ws.close();
      }
    });
    
    ws.on('close', () => {
      console.log('🔌 WebSocket closed');
    });
    
    // Keep running
    console.log('Signaling server is running. Press Ctrl+C to stop.');
    process.on('SIGINT', async () => {
      await server.stop();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Signaling test failed:', error);
    await server.stop();
  }
}

testSignaling();
```

Run it:
```bash
node test-signaling.js
```

**Expected Results:**
- Server starts on port 8080
- WebSocket connection succeeds
- Welcome message is received

### Step 3: Test Script Injection

Create a test file `test-injection.js`:

```javascript
import { BrowserManager } from './poc-streaming-system/browser-manager.js';
import { ScriptInjector } from './poc-streaming-system/script-injector.js';
import { SignalingServer } from './poc-streaming-system/signaling-server.js';

async function testInjection() {
  console.log('Testing Script Injection...');
  
  const browserManager = new BrowserManager({ port: 9222, headless: false });
  const signalingServer = new SignalingServer({ port: 8080 });
  
  try {
    // Start components
    await browserManager.launchBrowser();
    await browserManager.createControlTab();
    await signalingServer.start();
    
    const scriptInjector = new ScriptInjector(browserManager, signalingServer);
    
    // Inject control tab script
    await scriptInjector.injectControlTabScript('ws://localhost:8080');
    console.log('✅ Control tab script injected');
    
    // Create and inject target tab script
    const targetTab = await browserManager.createTargetTab('https://example.com');
    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for page load
    
    await scriptInjector.injectScript(targetTab.id, 'ws://localhost:8080');
    console.log('✅ Target tab script injected');
    
    console.log('Scripts injected. Check browser console for logs.');
    console.log('Press Ctrl+C to stop.');
    
    process.on('SIGINT', async () => {
      await scriptInjector.cleanup();
      await browserManager.cleanup();
      await signalingServer.stop();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Injection test failed:', error);
    await browserManager.cleanup();
    await signalingServer.stop();
  }
}

testInjection();
```

Run it:
```bash
node test-injection.js
```

**Expected Results:**
- Scripts are injected into both control and target tabs
- Check browser console (F12) for POC-Streaming log messages
- WebSocket connections should be established

### Step 4: Test Web Client

Create a simple HTML file `test-client.html`:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Test Web Client</title>
</head>
<body>
    <h1>POC Streaming Test Client</h1>
    <button id="connect">Connect to Signaling Server</button>
    <div id="status">Disconnected</div>
    <div id="logs"></div>

    <script>
        const connectBtn = document.getElementById('connect');
        const status = document.getElementById('status');
        const logs = document.getElementById('logs');
        
        let ws = null;
        
        function log(message) {
            const div = document.createElement('div');
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.appendChild(div);
        }
        
        connectBtn.addEventListener('click', () => {
            if (ws) {
                ws.close();
                return;
            }
            
            ws = new WebSocket('ws://localhost:8080');
            
            ws.onopen = () => {
                status.textContent = 'Connected';
                connectBtn.textContent = 'Disconnect';
                log('Connected to signaling server');
                
                ws.send(JSON.stringify({
                    type: 'register-web-client',
                    metadata: { test: true }
                }));
            };
            
            ws.onmessage = (event) => {
                const message = JSON.parse(event.data);
                log(`Received: ${message.type}`);
                
                if (message.type === 'available-streams') {
                    log(`Available tabs: ${message.targetTabs.length}`);
                }
            };
            
            ws.onclose = () => {
                status.textContent = 'Disconnected';
                connectBtn.textContent = 'Connect to Signaling Server';
                log('Disconnected from signaling server');
                ws = null;
            };
            
            ws.onerror = (error) => {
                log(`WebSocket error: ${error}`);
            };
        });
    </script>
</body>
</html>
```

Open this file in your browser and test the connection.

## 🐛 Troubleshooting

### Common Issues

1. **Chrome fails to start**
   ```bash
   # Check if Chrome is installed
   ls "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"  # macOS
   which google-chrome  # Linux
   ```

2. **Port already in use**
   ```bash
   # Check what's using the port
   lsof -i :9222  # CDP port
   lsof -i :8080  # Signaling port
   
   # Kill processes if needed
   kill -9 <PID>
   ```

3. **Permission denied errors**
   ```bash
   # Fix file permissions
   chmod +x poc-streaming-system/*.js
   
   # Or run with different user
   sudo node poc-streaming-system/simple-test.js
   ```

4. **WebSocket connection fails**
   - Check firewall settings
   - Ensure signaling server is running
   - Try different port: `new SignalingServer({ port: 8081 })`

5. **Script injection fails**
   - Check browser console for errors
   - Ensure target tab has loaded completely
   - Try injecting after a delay

### Debug Mode

Add debug logging to any test:

```javascript
// Add at the top of your test file
process.env.DEBUG = 'true';

// Or add manual logging
console.log('Debug: Current step...');
```

### Browser Console Inspection

1. Open Chrome DevTools (F12) on any tab
2. Look for `[POC-Streaming]` log messages
3. Check Network tab for WebSocket connections
4. Monitor `chrome://webrtc-internals/` for WebRTC debugging

## ✅ Success Indicators

You'll know the system is working when you see:

1. **Browser Manager**: Chrome opens with multiple tabs
2. **Signaling Server**: WebSocket connections succeed
3. **Script Injection**: Console logs show `[POC-Streaming]` messages
4. **Web Client**: Can connect and see available tabs

## 🎯 Next Steps

Once basic testing works:

1. Run the full demo: `npm run poc-demo`
2. Run comprehensive tests: `npm run poc-test`
3. Try the web client interface at `http://localhost:3000`
4. Test streaming functionality with multiple tabs

## 📞 Getting Help

If you encounter issues:

1. Check the console output for specific error messages
2. Verify all prerequisites are installed
3. Try the manual testing steps one by one
4. Check the browser console for JavaScript errors
5. Monitor network connections and ports

The system is designed to be robust, but browser automation can be sensitive to system configuration. The step-by-step approach will help identify exactly where any issues occur.
