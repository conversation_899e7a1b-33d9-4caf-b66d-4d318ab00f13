/**
 * Simple test to debug script injection
 */

import { CDP, createTarget } from "../simple-cdp.js";
import fs from 'fs';

async function testScriptInjection() {
  console.log("🧪 Testing script injection...");

  try {
    // 1. Create a target tab
    console.log("🆕 Creating target tab...");
    const targetInfo = await createTarget("https://httpbin.org/html");
    console.log("Target created:", targetInfo.id);

    // Wait for the tab to load
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 2. Create a CDP client
    const cdpClient = new CDP(targetInfo);
    await cdpClient.connect();
    
    // Enable Runtime domain
    await cdpClient.Runtime.enable();
    
    console.log("💉 Injecting simple test script...");
    
    // First, inject a simple test
    const testResult = await cdpClient.Runtime.evaluate({
      expression: `
        window.testValue = "Hello from injected script!";
        console.log("Script injected successfully");
        "Script injection test completed"
      `,
      returnByValue: true
    });
    
    console.log("✅ Simple script result:", testResult.value);

    // Check if the value was set
    const checkResult = await cdpClient.Runtime.evaluate({
      expression: "window.testValue",
      returnByValue: true
    });
    
    console.log("✅ Test value check:", checkResult.value);

    // Now try to inject a small part of the control tab script
    console.log("💉 Injecting control tab manager class...");
    
    const managerScript = `
      console.log("Starting control tab manager injection...");
      
      class ControlTabManager {
        constructor() {
          console.log("ControlTabManager constructor called");
          this.test = "manager created";
        }
        
        getTest() {
          return this.test;
        }
      }
      
      window.pocControlTabManager = new ControlTabManager();
      console.log("Control tab manager created:", window.pocControlTabManager);
      "Manager injection completed"
    `;
    
    const managerResult = await cdpClient.Runtime.evaluate({
      expression: managerScript,
      returnByValue: true
    });
    
    console.log("✅ Manager script result:", managerResult.value);

    // Check if the manager was created
    const managerCheck = await cdpClient.Runtime.evaluate({
      expression: "typeof window.pocControlTabManager",
      returnByValue: true
    });
    
    console.log("✅ Manager type check:", managerCheck.value);

    if (managerCheck.value === "object") {
      const testMethodResult = await cdpClient.Runtime.evaluate({
        expression: "window.pocControlTabManager.getTest()",
        returnByValue: true
      });
      console.log("✅ Manager test method:", testMethodResult.value);
    }

    await cdpClient.close();

    console.log("\n🎉 Script injection test completed!");
    return true;

  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  }
}

// Run the test
testScriptInjection()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error("💥 Test runner failed:", error);
    process.exit(1);
  });
