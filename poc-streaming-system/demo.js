/**
 * POC Streaming System Demo
 *
 * Showcases the complete browser streaming system by automatically:
 * - Starting the POC streaming system
 * - Creating multiple target tabs with different websites
 * - Injecting scripts and establishing connections
 * - Opening the web client interface for viewing streams
 */

import { POCStreamingSystem } from "./main.js";
import { spawn } from "child_process";

export class StreamingDemo {
  constructor() {
    this.system = null;
    this.demoTabs = [];
    this.isRunning = false;

    // Demo configuration
    this.config = {
      browserPort: 9222,
      signalingPort: 8080,
      webServerPort: 3000,
      headless: false, // Keep visible for demo
      autoOpenBrowser: false, // Don't auto-launch Chrome
    };

    // Demo websites to showcase
    this.demoSites = [
      {
        url: "https://example.com",
        name: "Example.com",
        description: "Simple example website",
      },
      {
        url: "https://httpbin.org/html",
        name: "HTTPBin HTML",
        description: "HTTP testing service",
      },
    ];
  }

  /**
   * Start the complete demo
   */
  async startDemo() {
    console.log("🎬 Starting POC Browser Streaming System Demo...\n");

    try {
      // 1. Start servers only
      await this.startServers();

      // 2. Wait for Chrome
      await this.waitForChrome();

      // 3. Initialize browser components
      await this.initializeBrowser();

      // 4. Create demo target tabs
      await this.createDemoTabs();

      // 5. Inject scripts
      await this.injectScripts();

      // 6. Display demo information
      this.displayDemoInfo();

      // 7. Setup graceful shutdown
      this.setupShutdownHandlers();

      this.isRunning = true;
      console.log("🎉 Demo is now running! Press Ctrl+C to stop.\n");

      // Keep demo running
      await this.keepAlive();
    } catch (error) {
      console.error("❌ Demo failed to start:", error);
      await this.stopDemo();
      throw error;
    }
  }

  /**
   * Start servers only
   */
  async startServers() {
    console.log("🚀 Starting servers...");

    this.system = new POCStreamingSystem(this.config);
    await this.system.startSignalingServer();
    await this.system.startWebServer();

    console.log("✅ Servers started successfully");
    console.log("\n📋 Please launch Chrome with CDP enabled:");
    console.log(
      "   /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome \\"
    );
    console.log("     --remote-debugging-port=9222 \\");
    console.log("     --user-data-dir=./chrome-data \\");
    console.log("     --auto-accept-this-tab-capture \\");
    console.log("     --remote-allow-origins=* \\");
    console.log("     --disable-web-security\n");
  }

  /**
   * Wait for Chrome to connect
   */
  async waitForChrome() {
    console.log("⏳ Waiting for Chrome to connect...");

    const maxAttempts = 60;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const response = await fetch(
          `http://localhost:${this.config.browserPort}/json/version`
        );
        if (response.ok) {
          console.log("✅ Chrome connected successfully");
          return;
        }
      } catch (error) {
        // Chrome not ready yet
      }

      attempts++;
      await this.wait(1000);

      if (attempts % 10 === 0) {
        console.log(
          `  ⏳ Still waiting for Chrome... (${attempts}/${maxAttempts})`
        );
      }
    }

    throw new Error("Chrome did not connect within 60 seconds");
  }

  /**
   * Initialize browser components
   */
  async initializeBrowser() {
    console.log("🔧 Initializing browser components...");

    await this.system.initializeBrowserManager();
    await this.system.initializeScriptInjector();

    console.log("✅ Browser components initialized");
  }

  /**
   * Create demo target tabs
   */
  async createDemoTabs() {
    console.log("📑 Creating demo target tabs...");

    for (const site of this.demoSites) {
      try {
        console.log(`  📄 Creating tab for: ${site.name}`);
        const targetTab = await this.system.browserManager.createTargetTab(
          site.url
        );

        this.demoTabs.push({
          ...targetTab,
          siteInfo: site,
        });

        console.log(`  ✅ Created: ${site.name}`);

        // Stagger tab creation to avoid overwhelming the system
        await this.wait(1500);
      } catch (error) {
        console.error(
          `  ❌ Failed to create tab for ${site.name}:`,
          error.message
        );
      }
    }

    console.log(`✅ Created ${this.demoTabs.length} demo tabs`);
  }

  /**
   * Inject scripts into demo tabs
   */
  async injectScripts() {
    console.log("💉 Injecting scripts into demo tabs...");

    const signalingUrl = `ws://localhost:${this.config.signalingPort}`;

    for (const tab of this.demoTabs) {
      try {
        console.log(`  💉 Injecting script into: ${tab.siteInfo.name}`);
        await this.system.scriptInjector.injectScript(tab.id, signalingUrl);
        await this.wait(1000); // Wait between injections
        console.log(`  ✅ Script injected into: ${tab.siteInfo.name}`);
      } catch (error) {
        console.error(
          `  ❌ Failed to inject script into ${tab.siteInfo.name}:`,
          error.message
        );
      }
    }

    // Wait a bit for scripts to register
    console.log("⏳ Waiting for scripts to register...");
    await this.wait(3000);
    console.log("✅ Script injection completed");
  }

  /**
   * Open web client in browser
   */
  async openWebClient() {
    console.log("🌐 Opening web client in browser...");

    const webClientUrl = `http://localhost:${this.config.webServerPort}`;

    try {
      // Try to open in default browser
      const platform = process.platform;
      let command;

      if (platform === "darwin") {
        command = "open";
      } else if (platform === "win32") {
        command = "start";
      } else {
        command = "xdg-open";
      }

      spawn(command, [webClientUrl], { detached: true, stdio: "ignore" });
      console.log(`✅ Web client opened: ${webClientUrl}`);
    } catch (error) {
      console.log(
        `⚠️  Could not auto-open browser. Please visit: ${webClientUrl}`
      );
    }
  }

  /**
   * Display demo information
   */
  displayDemoInfo() {
    console.log("\n" + "═".repeat(60));
    console.log("🎬 POC BROWSER STREAMING SYSTEM DEMO");
    console.log("═".repeat(60));

    console.log("\n📊 System Status:");
    const stats = this.system.getSystemStats();
    console.log(`  • System Running: ${stats.isRunning ? "✅" : "❌"}`);
    console.log(`  • Target Tabs: ${stats.targetTabs}`);
    console.log(`  • Injected Scripts: ${stats.injectedTabs}`);
    console.log(
      `  • Signaling Clients: ${stats.signalingStats?.totalClients || 0}`
    );

    console.log("\n🌐 Access Points:");
    console.log(
      `  • Web Client: http://localhost:${this.config.webServerPort}`
    );
    console.log(
      `  • API Status: http://localhost:${this.config.webServerPort}/api/status`
    );
    console.log(
      `  • Signaling Server: ws://localhost:${this.config.signalingPort}`
    );
    console.log(`  • Browser CDP: http://localhost:${this.config.browserPort}`);

    console.log("\n📑 Demo Target Tabs:");
    this.demoTabs.forEach((tab, index) => {
      const tabInfo = this.system.targetTabs.get(tab.id);
      const status = tabInfo?.isInjected ? "✅ Ready" : "⏳ Loading";
      console.log(`  ${index + 1}. ${tab.siteInfo.name} - ${status}`);
      console.log(`     ${tab.siteInfo.url}`);
      console.log(`     ${tab.siteInfo.description}`);
    });

    console.log("\n🎯 How to Use:");
    console.log(
      "  1. Open the web client in your browser (should open automatically)"
    );
    console.log(
      '  2. Click "Connect to Server" to connect to the signaling server'
    );
    console.log("  3. You should see the available target tabs listed");
    console.log('  4. Click "Start Stream" on any tab to begin streaming');
    console.log('  5. The stream will appear in the "Active Streams" section');
    console.log("  6. You can stream multiple tabs simultaneously");

    console.log("\n🔧 Technical Features Demonstrated:");
    console.log("  • CDP-enabled browser management");
    console.log("  • Automatic script injection with persistence");
    console.log("  • WebSocket signaling for WebRTC negotiation");
    console.log("  • Multi-stream support with grid layout");
    console.log("  • Reconnection logic for tab reloads/navigation");
    console.log("  • Real-time connection state management");

    console.log("\n" + "═".repeat(60));
  }

  /**
   * Setup graceful shutdown handlers
   */
  setupShutdownHandlers() {
    const shutdown = async (signal) => {
      console.log(`\n👋 Received ${signal}, shutting down demo...`);
      await this.stopDemo();
      process.exit(0);
    };

    process.on("SIGINT", () => shutdown("SIGINT"));
    process.on("SIGTERM", () => shutdown("SIGTERM"));

    // Handle uncaught exceptions
    process.on("uncaughtException", async (error) => {
      console.error("💥 Uncaught exception:", error);
      await this.stopDemo();
      process.exit(1);
    });
  }

  /**
   * Keep the demo alive
   */
  async keepAlive() {
    return new Promise((resolve) => {
      // The demo will run until interrupted
      // Shutdown handlers will call stopDemo() and resolve this promise
    });
  }

  /**
   * Stop the demo
   */
  async stopDemo() {
    if (!this.isRunning) return;

    console.log("🛑 Stopping demo...");

    try {
      if (this.system) {
        await this.system.stop();
        this.system = null;
      }

      this.demoTabs = [];
      this.isRunning = false;

      console.log("✅ Demo stopped successfully");
    } catch (error) {
      console.error("❌ Error stopping demo:", error);
    }
  }

  /**
   * Wait for specified milliseconds
   */
  wait(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const demo = new StreamingDemo();

  // Parse command line arguments
  const args = process.argv.slice(2);

  if (args.includes("--help") || args.includes("-h")) {
    console.log("POC Browser Streaming System Demo");
    console.log("");
    console.log("Usage: node demo.js [options]");
    console.log("");
    console.log("Options:");
    console.log("  --headless          Run browser in headless mode");
    console.log("  --no-auto-open      Don't automatically open web client");
    console.log("  --browser-port      CDP port (default: 9222)");
    console.log("  --signaling-port    Signaling server port (default: 8080)");
    console.log("  --web-port          Web server port (default: 3000)");
    console.log("  --help, -h          Show this help message");
    console.log("");
    process.exit(0);
  }

  // Apply command line options
  if (args.includes("--headless")) {
    demo.config.headless = true;
  }
  if (args.includes("--no-auto-open")) {
    demo.config.autoOpenBrowser = false;
  }

  // Start the demo
  demo.startDemo().catch((error) => {
    console.error("💥 Demo failed:", error);
    process.exit(1);
  });
}
