/**
 * Working Demo for POC Streaming System
 * 
 * This demo starts the complete system and provides a simple web interface
 * to test the streaming functionality.
 */

import { BrowserManager } from './browser-manager.js';
import { SignalingServer } from './signaling-server.js';
import { ScriptInjector } from './script-injector.js';
import express from 'express';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class WorkingDemo {
  constructor() {
    this.browserManager = null;
    this.signalingServer = null;
    this.scriptInjector = null;
    this.webServer = null;
    
    this.config = {
      browserPort: 9222,
      signalingPort: 8080,
      webServerPort: 3000,
      headless: false
    };
    
    this.isRunning = false;
  }

  async start() {
    console.log('🚀 Starting POC Streaming System Working Demo...\n');
    
    try {
      // 1. Start Browser Manager
      console.log('📱 Starting Browser Manager...');
      this.browserManager = new BrowserManager({
        port: this.config.browserPort,
        headless: this.config.headless
      });
      await this.browserManager.launchBrowser();
      await this.browserManager.createControlTab();
      console.log('✅ Browser Manager started');

      // 2. Start Signaling Server
      console.log('📡 Starting Signaling Server...');
      this.signalingServer = new SignalingServer({
        port: this.config.signalingPort
      });
      await this.signalingServer.start();
      console.log('✅ Signaling Server started');

      // 3. Initialize Script Injector
      console.log('💉 Initializing Script Injector...');
      this.scriptInjector = new ScriptInjector(
        this.browserManager,
        this.signalingServer
      );
      
      // Inject control tab script
      const signalingUrl = `ws://localhost:${this.config.signalingPort}`;
      await this.scriptInjector.injectControlTabScript(signalingUrl);
      console.log('✅ Script Injector initialized');

      // 4. Start Web Server
      console.log('🌐 Starting Web Server...');
      await this.startWebServer();
      console.log('✅ Web Server started');

      // 5. Create demo target tabs
      console.log('📑 Creating demo target tabs...');
      await this.createDemoTabs();
      console.log('✅ Demo tabs created');

      this.isRunning = true;
      this.displayInfo();
      this.setupShutdownHandlers();

      console.log('\n🎉 Demo is running! Check the web interface or browser tabs.\n');
      
      // Keep demo alive
      await this.keepAlive();

    } catch (error) {
      console.error('❌ Demo failed to start:', error);
      await this.stop();
      throw error;
    }
  }

  async startWebServer() {
    const app = express();
    
    // Serve static files from web-client directory
    app.use(express.static(join(__dirname, 'web-client')));
    
    // API endpoints
    app.get('/api/status', (req, res) => {
      const stats = this.signalingServer.getStats();
      res.json({
        isRunning: this.isRunning,
        browserPort: this.config.browserPort,
        signalingPort: this.config.signalingPort,
        webServerPort: this.config.webServerPort,
        stats: stats,
        targetTabs: Array.from(this.browserManager.targetTabs.values()).map(tab => ({
          id: tab.id,
          url: tab.url,
          title: tab.title || 'Loading...',
          isInjected: tab.isInjected || false
        }))
      });
    });

    app.get('/api/targets', (req, res) => {
      const targets = Array.from(this.browserManager.targetTabs.values()).map(tab => ({
        id: tab.id,
        url: tab.url,
        title: tab.title || 'Loading...',
        isInjected: tab.isInjected || false
      }));
      res.json(targets);
    });

    app.post('/api/targets/create', express.json(), async (req, res) => {
      try {
        const { url } = req.body;
        if (!url) {
          return res.status(400).json({ error: 'URL is required' });
        }

        const targetTab = await this.browserManager.createTargetTab(url);
        
        // Wait a bit for page to load, then inject script
        setTimeout(async () => {
          try {
            const signalingUrl = `ws://localhost:${this.config.signalingPort}`;
            await this.scriptInjector.injectScript(targetTab.id, signalingUrl);
          } catch (error) {
            console.error('Failed to inject script into new tab:', error);
          }
        }, 3000);

        res.json({
          id: targetTab.id,
          url: targetTab.url,
          title: targetTab.title || 'Loading...'
        });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    return new Promise((resolve, reject) => {
      this.webServer = app.listen(this.config.webServerPort, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  async createDemoTabs() {
    const demoUrls = [
      'https://example.com',
      'https://httpbin.org/html',
      'https://jsonplaceholder.typicode.com'
    ];

    for (const url of demoUrls) {
      try {
        console.log(`  📄 Creating tab for: ${url}`);
        const targetTab = await this.browserManager.createTargetTab(url);
        
        // Wait for page to load, then inject script
        setTimeout(async () => {
          try {
            const signalingUrl = `ws://localhost:${this.config.signalingPort}`;
            await this.scriptInjector.injectScript(targetTab.id, signalingUrl);
            console.log(`  ✅ Script injected into: ${url}`);
          } catch (error) {
            console.error(`  ❌ Failed to inject script into ${url}:`, error.message);
          }
        }, 2000);

        // Stagger tab creation
        await this.wait(1000);

      } catch (error) {
        console.error(`  ❌ Failed to create tab for ${url}:`, error.message);
      }
    }
  }

  displayInfo() {
    console.log('\n' + '═'.repeat(60));
    console.log('🎬 POC STREAMING SYSTEM - WORKING DEMO');
    console.log('═'.repeat(60));
    
    console.log('\n🌐 Access Points:');
    console.log(`  • Web Client: http://localhost:${this.config.webServerPort}`);
    console.log(`  • API Status: http://localhost:${this.config.webServerPort}/api/status`);
    console.log(`  • Signaling Server: ws://localhost:${this.config.signalingPort}`);
    console.log(`  • Browser CDP: http://localhost:${this.config.browserPort}`);
    
    console.log('\n🎯 How to Test:');
    console.log('  1. Open the web client: http://localhost:3000');
    console.log('  2. Click "Connect to Server"');
    console.log('  3. You should see available target tabs');
    console.log('  4. Click "Start Stream" on any tab');
    console.log('  5. The stream should appear in the interface');
    
    console.log('\n🔧 Browser Tabs:');
    console.log('  • Control Tab: Manages WebRTC connections');
    console.log('  • Target Tabs: Content to be streamed');
    console.log('  • Check browser console for [POC-Streaming] logs');
    
    console.log('\n' + '═'.repeat(60));
  }

  setupShutdownHandlers() {
    const shutdown = async (signal) => {
      console.log(`\n👋 Received ${signal}, shutting down demo...`);
      await this.stop();
      process.exit(0);
    };
    
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  }

  async keepAlive() {
    return new Promise((resolve) => {
      // Demo runs until interrupted
    });
  }

  async stop() {
    if (!this.isRunning) return;
    
    console.log('🛑 Stopping demo...');
    
    try {
      if (this.webServer) {
        this.webServer.close();
      }
      
      if (this.scriptInjector) {
        await this.scriptInjector.cleanup();
      }
      
      if (this.browserManager) {
        await this.browserManager.cleanup();
      }
      
      if (this.signalingServer) {
        await this.signalingServer.stop();
      }
      
      this.isRunning = false;
      console.log('✅ Demo stopped successfully');
      
    } catch (error) {
      console.error('❌ Error stopping demo:', error);
    }
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const demo = new WorkingDemo();
  
  demo.start().catch(error => {
    console.error('💥 Demo failed:', error);
    process.exit(1);
  });
}
