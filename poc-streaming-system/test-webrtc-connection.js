/**
 * Test WebRTC Connection Script
 *
 * This script tests the complete WebRTC flow:
 * 1. Start signaling server
 * 2. Launch control tab
 * 3. Launch target tab
 * 4. Initiate streaming
 * 5. Verify video display in control tab
 */

import { SignalingServer } from "./signaling-server.js";
import { <PERSON><PERSON>erManager } from "./browser-manager.js";
import { ScriptInjector } from "./script-injector.js";

class WebRTCConnectionTest {
  constructor() {
    this.signalingServer = null;
    this.browserManager = null;
    this.scriptInjector = null;
    this.controlTabPage = null;
    this.targetTabPage = null;
  }

  async run() {
    try {
      console.log("🚀 Starting WebRTC Connection Test...");

      // Step 1: Start signaling server
      await this.startSignalingServer();

      // Step 2: Initialize browser manager
      await this.initializeBrowser();

      // Step 3: Launch control tab
      await this.launchControlTab();

      // Step 4: Launch target tab
      await this.launchTargetTab();

      // Step 5: Wait for connections
      await this.waitForConnections();

      // Step 6: Initiate streaming
      await this.initiateStreaming();

      // Step 7: Verify video display
      await this.verifyVideoDisplay();

      console.log("✅ WebRTC Connection Test completed successfully!");
    } catch (error) {
      console.error("❌ WebRTC Connection Test failed:", error);
      throw error;
    }
  }

  async startSignalingServer() {
    console.log("📡 Starting signaling server...");
    this.signalingServer = new SignalingServer({ port: 8080 });
    await this.signalingServer.start();

    // Wait a moment for server to be ready
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  async initializeBrowser() {
    console.log("🌐 Initializing browser...");
    this.browserManager = new BrowserManager();
    await this.browserManager.launchBrowser();

    this.scriptInjector = new ScriptInjector(this.browserManager.browser);
  }

  async launchControlTab() {
    console.log("📋 Launching control tab...");
    this.controlTabPage = await this.browserManager.browser.newPage();

    // Navigate to a simple page
    await this.controlTabPage.goto(
      "data:text/html,<html><head><title>Control Tab</title></head><body><h1>Control Tab</h1><p>This is the control tab for WebRTC streaming.</p></body></html>"
    );

    // Inject control tab script
    await this.scriptInjector.injectControlTabScript(this.controlTabPage);

    // Wait for control tab to connect
    await new Promise((resolve) => setTimeout(resolve, 2000));
  }

  async launchTargetTab() {
    console.log("📑 Launching target tab...");
    this.targetTabPage = await this.browserManager.browser.newPage();

    // Navigate to a test page with some content
    await this.targetTabPage.goto(
      'data:text/html,<html><head><title>Target Tab</title></head><body><h1>Target Tab Content</h1><p>This is the content that will be streamed.</p><div style="width:200px;height:200px;background:linear-gradient(45deg,red,blue);margin:20px;"></div></body></html>'
    );

    // Inject target tab script
    await this.scriptInjector.injectTargetTabScript(this.targetTabPage);

    // Wait for target tab to connect
    await new Promise((resolve) => setTimeout(resolve, 2000));
  }

  async waitForConnections() {
    console.log("⏳ Waiting for WebSocket connections...");

    // Check signaling server stats
    const stats = this.signalingServer.getStats();
    console.log("📊 Server stats:", stats);

    if (stats.totalClients < 2) {
      throw new Error(`Expected at least 2 clients, got ${stats.totalClients}`);
    }

    if (!stats.hasControlTab) {
      throw new Error("Control tab not connected");
    }

    if (stats.targetTabs === 0) {
      throw new Error("No target tabs connected");
    }

    console.log("✅ All connections established");
  }

  async initiateStreaming() {
    console.log("🎥 Initiating streaming...");

    // Simulate a web client requesting a stream
    // We'll do this by directly calling the signaling server
    const webClientId = "test-web-client-" + Date.now();

    // Register a fake web client
    this.signalingServer.clients.set(webClientId, {
      id: webClientId,
      ws: { readyState: 1 }, // Mock WebSocket.OPEN
      type: "web-client",
      metadata: {},
      connected: true,
      lastActivity: Date.now(),
    });

    this.signalingServer.webClients.add(webClientId);

    // Get the first target tab
    const targetTabIds = Array.from(this.signalingServer.targetTabs.keys());
    if (targetTabIds.length === 0) {
      throw new Error("No target tabs available");
    }

    const targetTabId = targetTabIds[0];
    console.log(`📤 Requesting stream for target tab: ${targetTabId}`);

    // Send start stream message
    this.signalingServer.handleStartStream(webClientId, {
      type: "start-stream",
      tabId: targetTabId,
    });

    // Wait for streaming to initialize
    await new Promise((resolve) => setTimeout(resolve, 5000));
  }

  async verifyVideoDisplay() {
    console.log("📺 Verifying video display in control tab...");

    // Check if control tab has video elements
    const videoElements = await this.controlTabPage.$$("video");
    console.log(`Found ${videoElements.length} video elements in control tab`);

    if (videoElements.length === 0) {
      throw new Error("No video elements found in control tab");
    }

    // Check if video is playing
    for (let i = 0; i < videoElements.length; i++) {
      const video = videoElements[i];
      const isPlaying = await video.evaluate(
        (v) => !v.paused && !v.ended && v.readyState > 2
      );
      const hasStream = await video.evaluate((v) => v.srcObject !== null);

      console.log(
        `Video ${i + 1}: playing=${isPlaying}, hasStream=${hasStream}`
      );

      if (hasStream) {
        console.log("✅ Video stream detected in control tab");
        return;
      }
    }

    throw new Error("No active video streams found in control tab");
  }

  async cleanup() {
    console.log("🧹 Cleaning up test environment...");

    if (this.browserManager) {
      await this.browserManager.cleanup();
    }

    if (this.signalingServer) {
      await this.signalingServer.stop();
    }
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new WebRTCConnectionTest();

  test
    .run()
    .then(() => {
      console.log("🎉 Test completed successfully!");
      return test.cleanup();
    })
    .catch(async (error) => {
      console.error("💥 Test failed:", error);
      await test.cleanup();
      process.exit(1);
    });
}

export { WebRTCConnectionTest };
