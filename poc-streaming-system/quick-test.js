/**
 * Quick Test - Just Signaling Server and Web Client
 * 
 * This test starts only the signaling server and web client
 * to test the WebRTC connection flow without browser automation
 */

import { SignalingServer } from './signaling-server.js';
import express from 'express';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class QuickTest {
  constructor() {
    this.signalingServer = null;
    this.webServer = null;
    
    this.config = {
      signalingPort: 8080,
      webServerPort: 3000
    };
  }

  async start() {
    console.log('🚀 Starting Quick Test - Signaling Server + Web Client...\n');
    
    try {
      // 1. Start Signaling Server
      console.log('📡 Starting Signaling Server...');
      this.signalingServer = new SignalingServer({
        port: this.config.signalingPort
      });
      await this.signalingServer.start();
      console.log('✅ Signaling Server started');

      // 2. Start Web Server
      console.log('🌐 Starting Web Server...');
      await this.startWebServer();
      console.log('✅ Web Server started');

      // 3. Create mock target tabs for testing
      this.createMockTargetTabs();

      this.displayInfo();
      this.setupShutdownHandlers();

      console.log('\n🎉 Quick test is running! Open http://localhost:3000 to test.\n');
      
      // Keep test alive
      await this.keepAlive();

    } catch (error) {
      console.error('❌ Quick test failed to start:', error);
      await this.stop();
      throw error;
    }
  }

  async startWebServer() {
    const app = express();
    
    // Serve static files from web-client directory
    app.use(express.static(join(__dirname, 'web-client')));
    
    // API endpoints
    app.get('/api/status', (req, res) => {
      const stats = this.signalingServer.getStats();
      res.json({
        isRunning: true,
        signalingPort: this.config.signalingPort,
        webServerPort: this.config.webServerPort,
        stats: stats,
        targetTabs: [
          {
            id: 'mock-tab-1',
            url: 'https://example.com',
            title: 'Mock Example Tab',
            isInjected: true
          },
          {
            id: 'mock-tab-2',
            url: 'https://httpbin.org/html',
            title: 'Mock HTTPBin Tab',
            isInjected: true
          }
        ]
      });
    });

    app.get('/api/targets', (req, res) => {
      res.json([
        {
          id: 'mock-tab-1',
          url: 'https://example.com',
          title: 'Mock Example Tab',
          isInjected: true
        },
        {
          id: 'mock-tab-2',
          url: 'https://httpbin.org/html',
          title: 'Mock HTTPBin Tab',
          isInjected: true
        }
      ]);
    });

    return new Promise((resolve, reject) => {
      this.webServer = app.listen(this.config.webServerPort, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  createMockTargetTabs() {
    console.log('🎭 Creating mock target tabs for testing...');
    
    // Simulate target tabs being registered
    this.signalingServer.targetTabs.set('mock-tab-1', 'mock-client-1');
    this.signalingServer.targetTabs.set('mock-tab-2', 'mock-client-2');
    
    // Simulate control tab
    this.signalingServer.controlTab = 'mock-control-tab';
    
    console.log('✅ Mock target tabs created');
  }

  displayInfo() {
    console.log('\n' + '═'.repeat(60));
    console.log('🧪 QUICK TEST - SIGNALING + WEB CLIENT');
    console.log('═'.repeat(60));
    
    console.log('\n🌐 Access Points:');
    console.log(`  • Web Client: http://localhost:${this.config.webServerPort}`);
    console.log(`  • API Status: http://localhost:${this.config.webServerPort}/api/status`);
    console.log(`  • Signaling Server: ws://localhost:${this.config.signalingPort}`);
    
    console.log('\n🎯 How to Test:');
    console.log('  1. Open the web client: http://localhost:3000');
    console.log('  2. Click "Connect to Server"');
    console.log('  3. You should see mock target tabs');
    console.log('  4. Click "Start Stream" to test WebRTC flow');
    console.log('  5. Check browser console for detailed logs');
    
    console.log('\n🔧 What This Tests:');
    console.log('  • WebSocket signaling connection');
    console.log('  • Web client interface');
    console.log('  • Stream request/response flow');
    console.log('  • WebRTC offer/answer exchange (simulated)');
    
    console.log('\n📝 Expected Behavior:');
    console.log('  • Connection should succeed');
    console.log('  • Mock tabs should appear');
    console.log('  • Stream requests should be logged');
    console.log('  • WebRTC negotiation should start (but fail without real streams)');
    
    console.log('\n' + '═'.repeat(60));
  }

  setupShutdownHandlers() {
    const shutdown = async (signal) => {
      console.log(`\n👋 Received ${signal}, shutting down test...`);
      await this.stop();
      process.exit(0);
    };
    
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  }

  async keepAlive() {
    return new Promise((resolve) => {
      // Test runs until interrupted
    });
  }

  async stop() {
    console.log('🛑 Stopping quick test...');
    
    try {
      if (this.webServer) {
        this.webServer.close();
      }
      
      if (this.signalingServer) {
        await this.signalingServer.stop();
      }
      
      console.log('✅ Quick test stopped successfully');
      
    } catch (error) {
      console.error('❌ Error stopping quick test:', error);
    }
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new QuickTest();
  
  test.start().catch(error => {
    console.error('💥 Quick test failed:', error);
    process.exit(1);
  });
}
