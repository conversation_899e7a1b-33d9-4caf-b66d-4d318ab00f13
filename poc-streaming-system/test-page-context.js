/**
 * Test to check page context and basic functionality
 */

import { CDP, createTarget } from "../simple-cdp.js";

async function testPageContext() {
  console.log("🧪 Testing page context...");

  try {
    // 1. Create a target tab
    console.log("🆕 Creating target tab...");
    const targetInfo = await createTarget("https://httpbin.org/html");
    console.log("Target created:", targetInfo.id);

    // Wait for the tab to load
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 2. Create a CDP client
    const cdpClient = new CDP(targetInfo);
    await cdpClient.connect();
    
    // Enable Runtime domain
    await cdpClient.Runtime.enable();
    
    console.log("🔍 Testing basic page context...");
    
    // Test basic JavaScript execution
    const basicTest = await cdpClient.Runtime.evaluate({
      expression: "2 + 2",
      returnByValue: true
    });
    
    console.log("✅ Basic math result:", basicTest);

    // Test document access
    const documentTest = await cdpClient.Runtime.evaluate({
      expression: "document.title",
      returnByValue: true
    });
    
    console.log("✅ Document title:", documentTest);

    // Test window object
    const windowTest = await cdpClient.Runtime.evaluate({
      expression: "typeof window",
      returnByValue: true
    });
    
    console.log("✅ Window type:", windowTest);

    // Test setting a global variable
    const setVarTest = await cdpClient.Runtime.evaluate({
      expression: `
        window.testVar = "Hello World";
        window.testVar;
      `,
      returnByValue: true
    });
    
    console.log("✅ Set variable result:", setVarTest);

    // Test getting the variable back
    const getVarTest = await cdpClient.Runtime.evaluate({
      expression: "window.testVar",
      returnByValue: true
    });
    
    console.log("✅ Get variable result:", getVarTest);

    // Test creating a simple object
    const objectTest = await cdpClient.Runtime.evaluate({
      expression: `
        window.testObj = { message: "test object created" };
        typeof window.testObj;
      `,
      returnByValue: true
    });
    
    console.log("✅ Object creation result:", objectTest);

    // Test accessing object property
    const propTest = await cdpClient.Runtime.evaluate({
      expression: "window.testObj.message",
      returnByValue: true
    });
    
    console.log("✅ Object property result:", propTest);

    await cdpClient.close();

    console.log("\n🎉 Page context test completed!");
    return true;

  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  }
}

// Run the test
testPageContext()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error("💥 Test runner failed:", error);
    process.exit(1);
  });
