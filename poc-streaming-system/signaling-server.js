/**
 * WebSocket Signaling Server for POC Streaming
 *
 * Handles WebRTC signaling between target tabs, control tab, and web clients
 * Manages connection state and provides reconnection logic
 */

import WebSocket, { WebSocketServer } from "ws";
import { EventEmitter } from "events";

// Simple UUID generator
function generateId() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export class SignalingServer extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      port: options.port || 8080,
      ...options,
    };

    this.wss = null;
    this.clients = new Map(); // clientId -> { ws, type, metadata }
    this.targetTabs = new Map(); // tabId -> clientId
    this.controlTab = null; // clientId of control tab
    this.webClients = new Set(); // Set of web client IDs
    this.scriptInjector = options.scriptInjector || null; // Script injector for removing scripts

    // Single-stream state - now using tabId as primary identifier
    this.currentStream = null; // { tabId, webClientId }
    this.availableTabs = new Map(); // tabId -> { title, url, clientId }

    this.isRunning = false;
  }

  /**
   * Start the signaling server
   */
  async start() {
    if (this.isRunning) {
      console.log("Signaling server already running");
      return;
    }

    console.log(`🚀 Starting signaling server on port ${this.options.port}...`);

    this.wss = new WebSocketServer({
      port: this.options.port,
      perMessageDeflate: false,
    });

    this.wss.on("connection", (ws, request) => {
      this.handleConnection(ws, request);
    });

    this.wss.on("error", (error) => {
      console.error("WebSocket server error:", error);
    });

    this.isRunning = true;
    console.log(
      `✅ Signaling server started on ws://localhost:${this.options.port}`
    );
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(ws, request) {
    const clientId = generateId();
    const clientInfo = {
      id: clientId,
      ws,
      type: null, // 'target-tab', 'control-tab', 'web-client'
      metadata: {},
      connected: true,
      lastActivity: Date.now(),
    };

    this.clients.set(clientId, clientInfo);
    console.log(`📱 Client connected: ${clientId}`);

    // Send welcome message
    this.sendToClient(clientId, {
      type: "welcome",
      clientId,
      timestamp: Date.now(),
    });

    // Setup message handling
    ws.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMessage(clientId, message);
      } catch (error) {
        console.error(`Invalid JSON from client ${clientId}:`, error);
        this.sendToClient(clientId, {
          type: "error",
          message: "Invalid JSON format",
        });
      }
    });

    // Handle disconnection
    ws.on("close", () => {
      this.handleDisconnection(clientId).catch(console.error);
    });

    ws.on("error", (error) => {
      console.error(`Client ${clientId} error:`, error);
      this.handleDisconnection(clientId).catch(console.error);
    });
  }

  /**
   * Handle incoming messages from clients
   */
  handleMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) {
      console.warn(`Message from unknown client: ${clientId}`);
      return;
    }

    client.lastActivity = Date.now();
    console.log(
      `📨 Message from ${clientId} (${client.type}): ${message.type}`
    );

    switch (message.type) {
      case "register-target-tab":
        this.handleRegisterTargetTab(clientId, message);
        break;
      case "register-control-tab":
        this.handleRegisterControlTab(clientId, message);
        break;
      case "register-web-client":
        this.handleRegisterWebClient(clientId, message);
        break;
      case "request-stream":
        this.handleRequestStream(clientId, message);
        break;
      case "stop-stream":
        this.handleStopStream(clientId, message);
        break;
      case "webrtc-offer":
        this.handleWebRTCOffer(clientId, message);
        break;
      case "webrtc-answer":
        this.handleWebRTCAnswer(clientId, message);
        break;
      case "webrtc-ice-candidate":
        this.handleWebRTCIceCandidate(clientId, message);
        break;
      case "webrtc-offer-from-target":
        this.handleWebRTCOfferFromTarget(clientId, message);
        break;
      case "webrtc-ice-candidate-from-target":
        this.handleWebRTCIceCandidateFromTarget(clientId, message);
        break;
      case "webrtc-answer-to-target":
        this.handleWebRTCAnswerToTarget(clientId, message);
        break;
      case "webrtc-ice-candidate-to-target":
        this.handleWebRTCIceCandidateToTarget(clientId, message);
        break;
      case "webrtc-offer-to-web-client":
        this.handleWebRTCOfferToWebClient(clientId, message);
        break;
      case "webrtc-ice-candidate-to-web-client":
        this.handleWebRTCIceCandidateToWebClient(clientId, message);
        break;
      case "webrtc-answer-from-web-client":
        this.handleWebRTCAnswerFromWebClient(clientId, message);
        break;
      case "webrtc-ice-candidate-from-web-client":
        this.handleWebRTCIceCandidateFromWebClient(clientId, message);
        break;
      case "tab-loaded":
        this.handleTabLoaded(clientId, message);
        break;
      case "tab-unloading":
        this.handleTabUnloading(clientId, message);
        break;
      case "streaming-ready":
        this.handleStreamingReady(clientId, message);
        break;
      case "streaming-stopped":
        this.handleStreamingStopped(clientId, message);
        break;
      default:
        console.warn(`Unknown message type from ${clientId}: ${message.type}`);
    }
  }

  /**
   * Register a target tab
   */
  handleRegisterTargetTab(clientId, message) {
    const client = this.clients.get(clientId);
    client.type = "target-tab";
    client.metadata = {
      tabId: message.tabId,
      url: message.url,
      title: message.title,
    };

    this.targetTabs.set(message.tabId, clientId);

    // Store in available tabs for single-stream mode
    this.availableTabs.set(message.tabId, {
      title: message.title,
      url: message.url,
      clientId: clientId,
    });

    console.log(`📑 Target tab registered: ${message.title} (${message.url})`);

    // Notify web clients about available tabs
    this.broadcastToType("web-client", {
      type: "available-tabs-updated",
      tabs: Array.from(this.availableTabs.entries()).map(([tabId, info]) => ({
        tabId,
        title: info.title,
        url: info.url,
      })),
    });
  }

  /**
   * Register the control tab
   */
  handleRegisterControlTab(clientId, message) {
    const client = this.clients.get(clientId);
    client.type = "control-tab";
    client.metadata = message.metadata || {};

    this.controlTab = clientId;

    console.log(`📋 Control tab registered: ${clientId}`);

    // Send current target tabs to control tab
    const targetTabs = Array.from(this.targetTabs.entries()).map(
      ([tabId, targetClientId]) => {
        const targetClient = this.clients.get(targetClientId);
        return {
          tabId,
          url: targetClient?.metadata?.url,
          title: targetClient?.metadata?.title,
          clientId: targetClientId,
        };
      }
    );

    this.sendToClient(clientId, {
      type: "target-tabs-list",
      targetTabs,
    });
  }

  /**
   * Register a web client
   */
  handleRegisterWebClient(clientId, message) {
    const client = this.clients.get(clientId);
    client.type = "web-client";
    client.metadata = message.metadata || {};

    this.webClients.add(clientId);

    console.log(`🌐 Web client registered: ${clientId}`);

    // Emit event for test monitoring
    this.emit("clientConnected", { id: clientId, type: "web-client" });

    // Send available target tabs to web client
    const targetTabs = Array.from(this.targetTabs.entries()).map(
      ([tabId, targetClientId]) => {
        const targetClient = this.clients.get(targetClientId);
        return {
          tabId,
          url: targetClient?.metadata?.url,
          title: targetClient?.metadata?.title,
          isStreaming: this.isTabStreaming(tabId),
        };
      }
    );

    this.sendToClient(clientId, {
      type: "available-streams",
      targetTabs,
    });
  }

  /**
   * Handle single-stream request from web client
   */
  async handleRequestStream(clientId, message) {
    const tabId = message.tabId;
    console.log(`🎬 Single-stream requested for tab: ${tabId}`);

    // Check if tab exists
    if (!this.availableTabs.has(tabId)) {
      this.sendToClient(clientId, {
        type: "error",
        message: `Tab not found: ${tabId}`,
      });
      return;
    }

    // Stop current stream if any
    if (this.currentStream) {
      await this.stopCurrentStream();
    }

    // Start new stream
    this.startSingleStream(tabId, clientId);
  }

  /**
   * Start a single stream
   */
  startSingleStream(tabId, webClientId) {
    const targetClientId = this.targetTabs.get(tabId);

    if (!targetClientId) {
      console.error(`Target client not found for tab: ${tabId}`);
      return;
    }

    // Set current stream - using tabId as primary identifier
    this.currentStream = {
      tabId,
      webClientId,
    };

    // Tell target tab to start streaming
    this.sendToClient(targetClientId, {
      type: "start-streaming",
      tabId,
    });

    console.log(`🎥 Single stream started for tab: ${tabId}`);
  }

  /**
   * Stop current stream
   */
  async stopCurrentStream() {
    if (!this.currentStream) return;

    const tabId = this.currentStream.tabId;
    console.log(`🛑 Stopping current stream for tab: ${tabId}`);

    // Notify target tab to stop
    const targetClientId = this.targetTabs.get(tabId);
    if (targetClientId) {
      this.sendToClient(targetClientId, {
        type: "stop-streaming",
        tabId: tabId,
      });
    }

    // Remove the injected script from the tab
    if (this.scriptInjector) {
      try {
        await this.scriptInjector.removeScript(tabId);
        console.log(`🗑️ Script removed from tab: ${tabId}`);
      } catch (error) {
        console.error(`❌ Failed to remove script from tab ${tabId}:`, error);
      }
    }

    this.currentStream = null;
  }

  /**
   * Handle stop stream request
   */
  async handleStopStream(clientId, message) {
    const tabId = message.tabId;

    if (!this.currentStream || this.currentStream.tabId !== tabId) {
      console.log(`❌ No active stream for tab: ${tabId}`);
      this.sendToClient(clientId, {
        type: "error",
        message: `No active stream for tab: ${tabId}`,
      });
      return;
    }

    console.log(`🛑 Stopping stream for tab: ${tabId}`);
    await this.stopCurrentStream();
  }

  /**
   * Handle WebRTC offer
   */
  handleWebRTCOffer(clientId, message) {
    console.log(
      `🤝 WebRTC offer from ${clientId} to ${message.targetClientId}`
    );

    if (message.targetClientId) {
      this.sendToClient(message.targetClientId, {
        type: "webrtc-offer",
        offer: message.offer,
        fromClientId: clientId,
        streamId: message.streamId,
      });
    }
  }

  /**
   * Handle WebRTC answer
   */
  handleWebRTCAnswer(clientId, message) {
    console.log(
      `🤝 WebRTC answer from ${clientId} to ${message.targetClientId}`
    );

    if (message.targetClientId) {
      this.sendToClient(message.targetClientId, {
        type: "webrtc-answer",
        answer: message.answer,
        fromClientId: clientId,
        streamId: message.streamId,
      });
    }
  }

  /**
   * Handle WebRTC ICE candidate
   */
  handleWebRTCIceCandidate(clientId, message) {
    console.log(
      `🧊 ICE candidate from ${clientId} to ${message.targetClientId}`
    );

    if (message.targetClientId) {
      this.sendToClient(message.targetClientId, {
        type: "webrtc-ice-candidate",
        candidate: message.candidate,
        fromClientId: clientId,
        streamId: message.streamId,
      });
    }
  }

  /**
   * Handle tab loaded event
   */
  handleTabLoaded(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.type === "target-tab") {
      client.metadata.url = message.url;
      client.metadata.title = message.title;

      console.log(`📄 Tab loaded: ${message.tabId} - ${message.url}`);

      // Notify other clients about the update
      this.broadcastToType("control-tab", {
        type: "target-tab-updated",
        tabId: message.tabId,
        url: message.url,
        title: message.title,
      });

      this.broadcastToType("web-client", {
        type: "target-tab-updated",
        tabId: message.tabId,
        url: message.url,
        title: message.title,
      });
    }
  }

  /**
   * Handle tab unloading event
   */
  handleTabUnloading(clientId, message) {
    console.log(`📤 Tab unloading: ${message.tabId}`);

    // Clean up any active streams for this tab
    for (const [streamId, stream] of this.streams) {
      if (stream.targetTabId === message.tabId) {
        this.streams.delete(streamId);
        console.log(`🗑️ Cleaned up stream: ${streamId}`);
      }
    }
  }

  /**
   * Handle streaming ready event
   */
  handleStreamingReady(clientId, message) {
    if (this.currentStream && this.currentStream.tabId === message.tabId) {
      console.log(`✅ Stream ready for tab: ${message.tabId}`);

      // Emit event for test monitoring
      this.emit("streamStarted", { tabId: message.tabId });

      // Notify web client that stream is ready
      this.sendToClient(this.currentStream.webClientId, {
        type: "stream-ready",
        tabId: message.tabId,
      });
    }
  }

  /**
   * Handle streaming stopped event
   */
  handleStreamingStopped(clientId, message) {
    if (this.currentStream && this.currentStream.tabId === message.tabId) {
      console.log(`🛑 Stream stopped for tab: ${message.tabId}`);

      // Notify web client
      this.sendToClient(this.currentStream.webClientId, {
        type: "stream-ended",
        tabId: message.tabId,
      });

      this.currentStream = null;
    }
  }

  /**
   * Handle client disconnection
   */
  async handleDisconnection(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`📱 Client disconnected: ${clientId} (${client.type})`);

    // Clean up based on client type
    if (client.type === "target-tab") {
      const tabId = client.metadata.tabId;
      this.targetTabs.delete(tabId);

      // Clean up current stream if it's from this tab
      if (this.currentStream && this.currentStream.tabId === tabId) {
        this.currentStream = null;
      }

      // Notify other clients
      this.broadcastToType("control-tab", {
        type: "target-tab-disconnected",
        tabId,
        clientId,
      });

      this.broadcastToType("web-client", {
        type: "target-tab-unavailable",
        tabId,
      });
    } else if (client.type === "control-tab") {
      this.controlTab = null;
    } else if (client.type === "web-client") {
      this.webClients.delete(clientId);

      // Stop current stream if this web client was using it
      if (this.currentStream && this.currentStream.webClientId === clientId) {
        await this.stopCurrentStream();
      }
    }

    this.clients.delete(clientId);
  }

  /**
   * Send message to a specific client
   */
  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Broadcast message to all clients of a specific type
   */
  broadcastToType(type, message) {
    for (const client of this.clients.values()) {
      if (client.type === type && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(JSON.stringify(message));
      }
    }
  }

  /**
   * Check if a tab is currently streaming
   */
  isTabStreaming(tabId) {
    return this.currentStream && this.currentStream.tabId === tabId;
  }

  /**
   * Get server statistics
   */
  getStats() {
    return {
      totalClients: this.clients.size,
      targetTabs: this.targetTabs.size,
      webClients: this.webClients.size,
      activeStreams: this.currentStream ? 1 : 0,
      hasControlTab: !!this.controlTab,
      availableTabs: this.availableTabs.size,
    };
  }

  /**
   * Stop the signaling server
   */
  async stop() {
    if (!this.isRunning) return;

    console.log("🛑 Stopping signaling server...");

    // Close all client connections
    for (const client of this.clients.values()) {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close();
      }
    }

    // Close server
    if (this.wss) {
      this.wss.close();
    }

    this.isRunning = false;
    console.log("✅ Signaling server stopped");
  }

  /**
   * Handle WebRTC offer from target tab (to be forwarded to control tab)
   */
  handleWebRTCOfferFromTarget(clientId, message) {
    console.log(`🎯 WebRTC offer from target tab: ${message.targetTabId}`);

    // Forward the offer to the control tab for dual-hop architecture
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-offer-from-target",
        offer: message.offer,
        targetTabId: message.targetTabId,
        webClientId: this.currentStream ? this.currentStream.webClientId : null,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC ICE candidate from target tab (to be forwarded to control tab)
   */
  handleWebRTCIceCandidateFromTarget(clientId, message) {
    console.log(`🧊 ICE candidate from target tab: ${message.targetTabId}`);

    // Forward the ICE candidate to the control tab for dual-hop architecture
    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-ice-candidate-from-target",
        candidate: message.candidate,
        targetTabId: message.targetTabId,
        webClientId: this.currentStream ? this.currentStream.webClientId : null,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC answer to target tab (from control tab)
   */
  handleWebRTCAnswerToTarget(clientId, message) {
    console.log(`📤 WebRTC answer to target tab: ${message.targetTabId}`);

    // For single-stream mode, find target tab from current stream
    if (
      this.currentStream &&
      this.currentStream.tabId === message.targetTabId
    ) {
      const targetClientId = this.targetTabs.get(this.currentStream.tabId);
      if (targetClientId) {
        this.sendToClient(targetClientId, {
          type: "webrtc-answer-to-target",
          answer: message.answer,
          targetTabId: message.targetTabId,
          fromClientId: clientId,
        });
      }
    }
  }

  /**
   * Handle WebRTC ICE candidate to target tab (from control tab)
   */
  handleWebRTCIceCandidateToTarget(clientId, message) {
    console.log(`🧊 ICE candidate to target tab: ${message.targetTabId}`);

    // For single-stream mode, find target tab from current stream
    if (
      this.currentStream &&
      this.currentStream.tabId === message.targetTabId
    ) {
      const targetClientId = this.targetTabs.get(this.currentStream.tabId);
      if (targetClientId) {
        this.sendToClient(targetClientId, {
          type: "webrtc-ice-candidate-to-target",
          candidate: message.candidate,
          targetTabId: message.targetTabId,
          fromClientId: clientId,
        });
      }
    }
  }

  /**
   * Handle WebRTC offer from control tab to web client
   */
  handleWebRTCOfferToWebClient(clientId, message) {
    console.log(
      `🎯 WebRTC offer from control tab to web client for tab: ${this.currentStream?.tabId}`
    );

    if (this.currentStream && this.currentStream.webClientId) {
      this.sendToClient(this.currentStream.webClientId, {
        type: "webrtc-offer",
        offer: message.offer,
        tabId: this.currentStream.tabId,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC ICE candidate from control tab to web client
   */
  handleWebRTCIceCandidateToWebClient(clientId, message) {
    console.log(
      `🧊 ICE candidate from control tab to web client for tab: ${this.currentStream?.tabId}`
    );

    if (this.currentStream && this.currentStream.webClientId) {
      this.sendToClient(this.currentStream.webClientId, {
        type: "webrtc-ice-candidate",
        candidate: message.candidate,
        tabId: this.currentStream.tabId,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC answer from web client to control tab
   */
  handleWebRTCAnswerFromWebClient(clientId, message) {
    console.log(
      `📤 WebRTC answer from web client to control tab for tab: ${message.tabId}`
    );

    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-answer-from-web-client",
        answer: message.answer,
        tabId: message.tabId,
        fromClientId: clientId,
      });
    }
  }

  /**
   * Handle WebRTC ICE candidate from web client to control tab
   */
  handleWebRTCIceCandidateFromWebClient(clientId, message) {
    console.log(
      `🧊 ICE candidate from web client to control tab for tab: ${message.tabId}`
    );

    if (this.controlTab) {
      this.sendToClient(this.controlTab, {
        type: "webrtc-ice-candidate-from-web-client",
        candidate: message.candidate,
        tabId: message.tabId,
        fromClientId: clientId,
      });
    }
  }
}
