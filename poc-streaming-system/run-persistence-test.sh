#!/bin/bash

# Stream Persistence Test Runner
# 
# This script helps run the stream persistence test with proper setup

echo "🧪 Stream Persistence Test Runner"
echo "=================================="

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if Chrome is running with CDP
echo "🔍 Checking if Chrome is running with CDP..."
if ! curl -s http://localhost:9222/json/version > /dev/null; then
    echo "⚠️  Chrome is not running with CDP enabled."
    echo ""
    echo "Please start Chrome with the following command:"
    echo "google-chrome \\"
    echo "  --remote-debugging-port=9222 \\"
    echo "  --user-data-dir=./chrome-data \\"
    echo "  --auto-accept-this-tab-capture \\"
    echo "  --remote-allow-origins=* \\"
    echo "  --disable-web-security"
    echo ""
    echo "Then run this script again."
    exit 1
fi

echo "✅ Chrome is running with CDP"

# Check if we're in the right directory
if [ ! -f "stream-persistence-test.js" ]; then
    echo "❌ stream-persistence-test.js not found. Please run this script from the poc-streaming-system directory."
    exit 1
fi

echo "📁 Current directory: $(pwd)"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

echo ""
echo "🚀 Starting Stream Persistence Test..."
echo ""
echo "📋 Test Features:"
echo "  ✅ Automatic stream initialization"
echo "  🔗 Persistent WebRTC connections"
echo "  🔄 Target tab reconnection simulation"
echo "  🎯 RTCRtpSender.replaceTrack() testing"
echo "  📺 Immediate playback for new clients"
echo ""
echo "🌐 After the test starts, open: http://localhost:3000"
echo ""
echo "⏹️  Press Ctrl+C to stop the test"
echo ""

# Run the test
node stream-persistence-test.js
