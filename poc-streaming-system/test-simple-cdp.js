/**
 * Test script to verify simple-cdp implementation in control tab script
 */

import { CDP, listTargets, createTarget } from "../simple-cdp.js";

async function testSimpleCDPImplementation() {
  console.log("🧪 Testing simple-cdp implementation...");

  try {
    // 1. List available targets
    console.log("📋 Listing available targets...");
    const targets = await listTargets();
    console.log(`Found ${targets.length} targets`);

    // 2. Create a new target tab
    console.log("🆕 Creating new target tab...");
    const targetInfo = await createTarget("https://example.com");
    console.log("Target created:", targetInfo.id);

    // Wait for the tab to load
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 3. Test CDP connection and session establishment
    console.log("🔗 Testing CDP connection...");
    const cdpClient = new CDP(targetInfo);
    await cdpClient.connect();
    console.log("✅ CDP client connected");

    // 4. Test attachToTarget for session management
    console.log("📎 Testing attachToTarget...");
    const attachResult = await cdpClient.Target.attachToTarget({
      targetId: targetInfo.id,
      flatten: true
    });
    const sessionId = attachResult.sessionId;
    console.log("✅ Session established:", sessionId);

    // 5. Test Runtime.evaluate with session
    console.log("🔍 Testing Runtime.evaluate...");
    const evalResult = await cdpClient.Runtime.evaluate({
      expression: `({
        width: window.innerWidth,
        height: window.innerHeight,
        url: window.location.href,
        title: document.title
      })`,
      returnByValue: true
    }, sessionId);
    console.log("✅ Runtime.evaluate result:", evalResult.value);

    // 6. Test Input.dispatchMouseEvent
    console.log("🖱️  Testing Input.dispatchMouseEvent...");
    await cdpClient.Input.dispatchMouseEvent({
      type: "mousePressed",
      x: 100,
      y: 100,
      button: "left",
      clickCount: 1,
      buttons: 1
    }, sessionId);

    await new Promise(resolve => setTimeout(resolve, 50));

    await cdpClient.Input.dispatchMouseEvent({
      type: "mouseReleased",
      x: 100,
      y: 100,
      button: "left",
      clickCount: 1,
      buttons: 0
    }, sessionId);
    console.log("✅ Mouse event dispatched successfully");

    // 7. Test cleanup
    console.log("🧹 Testing cleanup...");
    await cdpClient.close();
    console.log("✅ CDP client closed");

    console.log("\n🎉 All simple-cdp tests passed!");
    return true;

  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  }
}

// Run the test
testSimpleCDPImplementation()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error("💥 Test runner failed:", error);
    process.exit(1);
  });
