/**
 * WebRTC Demo Script
 *
 * This script demonstrates the complete WebRTC streaming flow:
 * 1. Target tab captures its content using getDisplayMedia
 * 2. Control tab receives and displays the stream
 * 3. Web client can also receive the stream
 */

import { SignalingServer } from "./signaling-server.js";
import { BrowserManager } from "./browser-manager.js";
import fs from "fs";

class WebRTCDemo {
  constructor() {
    this.signalingServer = null;
    this.browserManager = null;
    this.scriptInjector = null;
    this.controlTabPage = null;
    this.targetTabPage = null;
    this.webClientPage = null;
  }

  async start() {
    try {
      console.log("🚀 Starting WebRTC Demo...");

      // Start signaling server
      console.log("📡 Starting signaling server...");
      this.signalingServer = new SignalingServer({ port: 8080 });
      await this.signalingServer.start();
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Initialize browser
      console.log("🌐 Launching browser...");
      this.browserManager = new BrowserManager();
      await this.browserManager.launchBrowser();

      // Launch control tab
      console.log("📋 Setting up control tab...");
      this.controlTabPage = await this.browserManager.browser.newPage();
      await this.controlTabPage.goto(
        "data:text/html,<html><head><title>Control Tab</title></head><body><h1>🎛️ Control Tab</h1><p>This tab manages WebRTC connections and displays streams.</p></body></html>"
      );
      await this.injectControlTabScript(this.controlTabPage);

      // Launch target tab with interesting content
      console.log("📑 Setting up target tab...");
      this.targetTabPage = await this.browserManager.browser.newPage();
      await this.targetTabPage.goto(
        "data:text/html," +
          encodeURIComponent(`
        <html>
          <head>
            <title>Target Tab - Animated Content</title>
            <style>
              body { 
                font-family: Arial, sans-serif; 
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
                background-size: 400% 400%;
                animation: gradientShift 4s ease infinite;
                margin: 0;
                padding: 20px;
              }
              @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
              }
              .content {
                background: rgba(255, 255, 255, 0.9);
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
              }
              .spinner {
                width: 100px;
                height: 100px;
                border: 5px solid #f3f3f3;
                border-top: 5px solid #3498db;
                border-radius: 50%;
                animation: spin 2s linear infinite;
                margin: 20px auto;
              }
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
              .counter {
                font-size: 24px;
                font-weight: bold;
                text-align: center;
                color: #2c3e50;
              }
            </style>
          </head>
          <body>
            <div class="content">
              <h1>🎯 Target Tab - Live Content</h1>
              <p>This content is being captured and streamed via WebRTC!</p>
              <div class="spinner"></div>
              <div class="counter" id="counter">0</div>
            </div>
            <script>
              let count = 0;
              setInterval(() => {
                document.getElementById('counter').textContent = ++count;
              }, 1000);
            </script>
          </body>
        </html>
      `)
      );
      await this.injectTargetTabScript(this.targetTabPage);

      // Launch web client
      console.log("🌐 Setting up web client...");
      this.webClientPage = await this.browserManager.browser.newPage();
      await this.webClientPage.goto(
        "http://localhost:8080/../web-client/index.html"
      );

      // Wait for all connections
      console.log("⏳ Waiting for connections...");
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Check connections
      const stats = this.signalingServer.getStats();
      console.log("📊 Connection stats:", stats);

      if (stats.totalClients >= 2) {
        console.log("✅ All components connected successfully!");
        console.log("");
        console.log("🎉 Demo is ready!");
        console.log("");
        console.log("📋 Control Tab: Check the floating panel on the right");
        console.log("📑 Target Tab: Content is ready to be streamed");
        console.log("🌐 Web Client: Use the interface to start streaming");
        console.log("");
        console.log("To test:");
        console.log("1. Go to the web client tab");
        console.log('2. Click "Connect" to connect to signaling server');
        console.log('3. Click "Start Stream" on the target tab');
        console.log(
          "4. Watch the stream appear in both web client and control tab"
        );
        console.log("");
        console.log("Press Ctrl+C to stop the demo");

        // Keep the demo running
        await this.keepAlive();
      } else {
        throw new Error(
          `Expected at least 2 clients, got ${stats.totalClients}`
        );
      }
    } catch (error) {
      console.error("❌ Demo failed:", error);
      await this.cleanup();
      process.exit(1);
    }
  }

  async keepAlive() {
    // Keep the demo running until interrupted
    return new Promise((resolve) => {
      process.on("SIGINT", () => {
        console.log("\n🛑 Stopping demo...");
        resolve();
      });

      // Also log periodic stats
      const statsInterval = setInterval(() => {
        const stats = this.signalingServer.getStats();
        console.log(
          `📊 [${new Date().toLocaleTimeString()}] Clients: ${
            stats.totalClients
          }, Streams: ${stats.activeStreams}, Target Tabs: ${stats.targetTabs}`
        );
      }, 30000); // Every 30 seconds

      process.on("SIGINT", () => {
        clearInterval(statsInterval);
      });
    });
  }

  async injectControlTabScript(page) {
    console.log("💉 Injecting control tab script...");
    const script = fs.readFileSync("./control-tab-script.js", "utf8");
    const scriptWithUrl = script.replace(
      "${SIGNALING_SERVER_URL}",
      "ws://localhost:8080"
    );
    await page.evaluate(scriptWithUrl);
  }

  async injectTargetTabScript(page) {
    console.log("💉 Injecting target tab script...");
    const script = fs.readFileSync("./target-tab-streamer.js", "utf8");
    const scriptWithUrl = script.replace(
      "${SIGNALING_SERVER_URL}",
      "ws://localhost:8080"
    );
    await page.evaluate(scriptWithUrl);
  }

  async cleanup() {
    console.log("🧹 Cleaning up...");

    if (this.browserManager) {
      await this.browserManager.cleanup();
    }

    if (this.signalingServer) {
      await this.signalingServer.stop();
    }

    console.log("✅ Cleanup complete");
  }
}

// Run the demo
const demo = new WebRTCDemo();

demo
  .start()
  .then(() => demo.cleanup())
  .catch(async (error) => {
    console.error("💥 Demo error:", error);
    await demo.cleanup();
    process.exit(1);
  });
