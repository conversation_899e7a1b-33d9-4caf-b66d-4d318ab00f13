# Stream Persistence Test

A comprehensive test script that demonstrates WebRTC stream persistence between control tab and web clients with automatic stream initialization and target tab reconnection simulation.

## 🎯 Test Objectives

This test validates the following key requirements:

### ✅ Core Functionality
- **Automatic Stream Initialization**: Programmatically uncomments `handleStartStream()` in target-tab-streamer.js
- **Immediate Playback**: Web clients automatically start displaying streams as soon as they connect
- **Persistent Connections**: WebRTC connections between control tab and web clients remain stable

### 🔄 Resilience Testing
- **Target Tab Disconnection/Reconnection**: Simulates navigation scenarios where target tabs lose connection
- **RTCRtpSender.replaceTrack()**: Seamlessly replaces video tracks when target tab reconnects
- **Dual-Hop Architecture**: Tests the resilience of target tab ↔ control tab ↔ web clients architecture

### 📊 Monitoring & Validation
- **Connection State Tracking**: Monitors WebRTC connection states throughout the test
- **Stream Quality Verification**: Ensures video streams maintain quality during reconnections
- **Multi-Client Support**: Tests multiple web clients receiving the same stream simultaneously

## 🚀 Quick Start

### Prerequisites

1. **Chrome with CDP enabled**:
   ```bash
   google-chrome \
     --remote-debugging-port=9222 \
     --user-data-dir=./chrome-data \
     --auto-accept-this-tab-capture \
     --remote-allow-origins=* \
     --disable-web-security
   ```

2. **Node.js** (v16 or higher)

### Running the Test

#### Option 1: Using the Test Runner (Recommended)
```bash
cd poc-streaming-system
./run-persistence-test.sh
```

#### Option 2: Direct Execution
```bash
cd poc-streaming-system
node stream-persistence-test.js
```

### Test Interaction

1. **Start the test** - The script will automatically:
   - Launch the POC streaming system
   - Create target tabs (YouTube, GitHub)
   - Inject scripts with automatic streaming enabled
   - Set up monitoring for test events

2. **Open web client** - Navigate to `http://localhost:3000`
   - You should see streams automatically playing
   - Multiple browser windows can be opened to test multi-client support

3. **Observe automated tests** - The script will automatically:
   - Simulate target tab disconnections
   - Test reconnection scenarios
   - Verify track replacement functionality
   - Monitor connection persistence

## 🧪 Test Phases

### Phase 1: Initialization
- ✅ Start POC streaming system
- ✅ Create target tabs with auto-streaming
- ✅ Verify initial stream establishment

### Phase 2: Persistence Testing
- 🔄 Simulate target tab disconnection
- 🔗 Verify control-to-web-client connections persist
- 📺 Confirm video continues playing in web clients

### Phase 3: Reconnection Testing
- 🔌 Simulate target tab reconnection
- 🎯 Test RTCRtpSender.replaceTrack() functionality
- ✅ Verify seamless stream switching

### Phase 4: Multi-Client Testing
- 👥 Test multiple web client connections
- 📊 Verify all clients receive the same stream
- 🔗 Confirm connection efficiency

## 📋 Test Results

The test provides real-time feedback on:

```
📊 STREAM PERSISTENCE TEST RESULTS
==================================
✅ PASSED - stream initialization
✅ PASSED - persistent connections  
✅ PASSED - target reconnection
✅ PASSED - track replacement
✅ PASSED - automatic playback

📈 Overall Score: 5/5 tests passed
```

## 🔧 Technical Implementation

### Enhanced Components

#### 1. Script Injector (`script-injector.js`)
- **New Methods**:
  - `getScriptContent()`: Retrieves target-tab-streamer.js content
  - `injectCustomScript()`: Injects modified scripts with auto-streaming

#### 2. Control Tab Script (`control-tab-script.js`)
- **New Features**:
  - `replaceTracksInPersistentConnection()`: Implements RTCRtpSender.replaceTrack()
  - `handleTargetTabReconnected()`: Manages reconnection scenarios
  - Persistent connection tracking with `persistentConnections` Map

#### 3. Signaling Server (`signaling-server.js`)
- **Event Emission**: Extends EventEmitter for test monitoring
- **New Events**: `clientConnected`, `streamStarted`, `connectionPersisted`

### Key Architectural Features

#### Dual-Hop WebRTC Architecture
```
Target Tab ←→ Control Tab ←→ Web Clients
     ↑              ↑              ↑
  Reconnects    Persistent     Multiple
  on reload    Connection     Clients
```

#### RTCRtpSender.replaceTrack() Flow
1. Target tab disconnects (navigation/reload)
2. Control tab preserves connections to web clients
3. Target tab reconnects with new stream
4. Control tab uses `replaceTrack()` to seamlessly switch video tracks
5. Web clients continue receiving video without interruption

## 🐛 Troubleshooting

### Common Issues

#### Chrome Not Starting
- Ensure Chrome is closed before running with CDP flags
- Check that port 9222 is not in use
- Verify user-data-dir permissions

#### Streams Not Auto-Starting
- Check console for script injection errors
- Verify `handleStartStream()` is being uncommented
- Ensure tab capture permissions are granted

#### Track Replacement Failing
- Monitor browser console for WebRTC errors
- Check that RTCRtpSender.replaceTrack() is supported
- Verify stream tracks are available for replacement

### Debug Mode

Enable verbose logging by setting environment variable:
```bash
DEBUG=poc-streaming node stream-persistence-test.js
```

## 🔮 Future Enhancements

- **Automated Browser Control**: Use Puppeteer to automatically open web clients
- **Performance Metrics**: Add latency and quality measurements
- **Stress Testing**: Test with many concurrent connections
- **Mobile Testing**: Verify functionality on mobile browsers
- **Network Simulation**: Test under various network conditions

## 📚 Related Documentation

- [WebRTC Implementation Summary](./WEBRTC_IMPLEMENTATION_SUMMARY.md)
- [Testing Guide](./TESTING_GUIDE.md)
- [Main POC Documentation](./README.md)
