/**
 * Test script to verify the control tab script's simple-cdp implementation
 */

import { CDP, listTargets, createTarget } from "../simple-cdp.js";
import fs from 'fs';

async function testControlTabCDPImplementation() {
  console.log("🧪 Testing control tab script's simple-cdp implementation...");

  try {
    // 1. Create a target tab
    console.log("🆕 Creating target tab...");
    const targetInfo = await createTarget("https://httpbin.org/html");
    console.log("Target created:", targetInfo.id);

    // Wait for the tab to load
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 2. Load and inject the control tab script
    console.log("📝 Loading control tab script...");
    const controlTabScript = fs.readFileSync('./control-tab-script.js', 'utf8');
    
    // Create a CDP client to inject the script
    const cdpClient = new CDP(targetInfo);
    await cdpClient.connect();
    
    // Enable Runtime domain
    await cdpClient.Runtime.enable();
    
    console.log("💉 Injecting control tab script...");
    await cdpClient.Runtime.evaluate({
      expression: controlTabScript,
      returnByValue: false
    });
    
    console.log("✅ Control tab script injected");

    // 3. Test if the control tab manager was created
    console.log("🔍 Testing control tab manager creation...");
    const managerCheck = await cdpClient.Runtime.evaluate({
      expression: "typeof window.pocControlTabManager",
      returnByValue: true
    });
    
    if (managerCheck.value !== "object") {
      throw new Error("Control tab manager not created");
    }
    console.log("✅ Control tab manager created successfully");

    // 4. Test CDP session establishment
    console.log("🔗 Testing CDP session establishment...");
    const sessionTest = await cdpClient.Runtime.evaluate({
      expression: `
        (async () => {
          try {
            const manager = window.pocControlTabManager;
            const cdpClient = await manager.establishCDPSession("${targetInfo.id}");
            return { success: true, hasClient: !!cdpClient };
          } catch (error) {
            return { success: false, error: error.message };
          }
        })()
      `,
      returnByValue: true,
      awaitPromise: true
    });

    if (!sessionTest.value.success) {
      throw new Error(`CDP session establishment failed: ${sessionTest.value.error}`);
    }
    console.log("✅ CDP session established successfully");

    // 5. Test tab info retrieval
    console.log("📊 Testing tab info retrieval...");
    const tabInfoTest = await cdpClient.Runtime.evaluate({
      expression: `
        (async () => {
          try {
            const manager = window.pocControlTabManager;
            const tabInfo = await manager.getTargetTabInfo("${targetInfo.id}");
            return { success: true, tabInfo };
          } catch (error) {
            return { success: false, error: error.message };
          }
        })()
      `,
      returnByValue: true,
      awaitPromise: true
    });

    if (!tabInfoTest.value.success) {
      throw new Error(`Tab info retrieval failed: ${tabInfoTest.value.error}`);
    }
    console.log("✅ Tab info retrieved:", tabInfoTest.value.tabInfo);

    // 6. Test script execution
    console.log("📜 Testing script execution...");
    const scriptTest = await cdpClient.Runtime.evaluate({
      expression: `
        (async () => {
          try {
            const manager = window.pocControlTabManager;
            const result = await manager.executeCDPScript("${targetInfo.id}", "document.title");
            return { success: true, result };
          } catch (error) {
            return { success: false, error: error.message };
          }
        })()
      `,
      returnByValue: true,
      awaitPromise: true
    });

    if (!scriptTest.value.success) {
      throw new Error(`Script execution failed: ${scriptTest.value.error}`);
    }
    console.log("✅ Script executed successfully:", scriptTest.value.result);

    // 7. Test cleanup
    console.log("🧹 Testing cleanup...");
    const cleanupTest = await cdpClient.Runtime.evaluate({
      expression: `
        (async () => {
          try {
            const manager = window.pocControlTabManager;
            await manager.cleanupCDPSession("${targetInfo.id}");
            return { success: true };
          } catch (error) {
            return { success: false, error: error.message };
          }
        })()
      `,
      returnByValue: true,
      awaitPromise: true
    });

    if (!cleanupTest.value.success) {
      throw new Error(`Cleanup failed: ${cleanupTest.value.error}`);
    }
    console.log("✅ Cleanup completed successfully");

    // Close our test CDP client
    await cdpClient.close();

    console.log("\n🎉 All control tab CDP tests passed!");
    console.log("✨ The simple-cdp implementation is working correctly!");
    return true;

  } catch (error) {
    console.error("❌ Test failed:", error);
    return false;
  }
}

// Run the test
testControlTabCDPImplementation()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error("💥 Test runner failed:", error);
    process.exit(1);
  });
