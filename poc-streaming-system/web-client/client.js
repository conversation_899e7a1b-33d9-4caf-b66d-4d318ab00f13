/**
 * POC Browser Streaming Web Client
 *
 * Connects to signaling server and displays multiple streams in grid layout
 * Handles WebRTC connections and stream management
 */

class StreamingClient {
  constructor() {
    this.signalingServerUrl = "ws://localhost:8080";
    this.websocket = null;
    this.isConnected = false;
    this.clientId = null;

    // WebRTC configuration
    this.rtcConfig = {
      iceServers: [
        { urls: "stun:stun.cloudflare.com:3478" },
        { urls: "stun:stun.l.google.com:19302" },
      ],
      iceCandidatePoolSize: 10,
    };

    // Single-stream state management
    this.availableTabs = new Map(); // tabId -> tabInfo
    this.currentStream = null; // { peerConnection, stream, tabId }
    this.currentTabId = null;
    this.dataChannel = null; // WebRTC data channel for user events

    // UI elements
    this.connectBtn = document.getElementById("connectBtn");
    this.disconnectBtn = document.getElementById("disconnectBtn");
    this.statusIndicator = document.getElementById("statusIndicator");
    this.statusText = document.getElementById("statusText");
    this.tabsGrid = document.getElementById("tabsGrid");
    this.currentStreamVideo = document.getElementById("currentStreamVideo");
    this.streamInfo = document.getElementById("streamInfo");
    this.logsContainer = document.getElementById("logsContainer");

    this.init();
  }

  init() {
    this.log("info", "Initializing streaming client...");
    this.setupEventListeners();
    this.updateUI();
  }

  setupEventListeners() {
    this.connectBtn.addEventListener("click", () => this.connect());
    this.disconnectBtn.addEventListener("click", () => this.disconnect());

    // Handle page unload
    window.addEventListener("beforeunload", () => {
      this.disconnect();
    });
  }

  async connect() {
    if (this.isConnected) {
      this.log("warn", "Already connected to signaling server");
      return;
    }

    this.log("info", "Connecting to signaling server...");
    this.updateStatus("connecting", "Connecting...");

    try {
      this.websocket = new WebSocket(this.signalingServerUrl);

      this.websocket.onopen = () => {
        this.log("success", "Connected to signaling server");
        this.isConnected = true;
        this.updateStatus("connected", "Connected");
        this.updateUI();
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          this.log(
            "error",
            `Failed to parse message: ${error.message} -> ${event.data}`
          );
        }
      };

      this.websocket.onclose = () => {
        this.log("warn", "Disconnected from signaling server");
        this.isConnected = false;
        this.clientId = null;
        this.updateStatus("disconnected", "Disconnected");
        this.updateUI();
        this.clearStreams();
      };

      this.websocket.onerror = (error) => {
        this.log(
          "error",
          `WebSocket error: ${error.message || "Unknown error"}`
        );
        this.updateStatus("error", "Connection Error");
      };
    } catch (error) {
      this.log("error", `Failed to connect: ${error.message}`);
      this.updateStatus("error", "Connection Failed");
    }
  }

  disconnect() {
    if (!this.isConnected) return;

    this.log("info", "Disconnecting from signaling server...");

    // Close all peer connections
    this.clearStreams();

    // Close WebSocket
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.isConnected = false;
    this.clientId = null;
    this.updateStatus("disconnected", "Disconnected");
    this.updateUI();
  }

  handleMessage(message) {
    this.log("info", `Received: ${message.type}`);

    switch (message.type) {
      case "welcome":
        this.handleWelcome(message);
        break;
      case "available-streams":
        this.handleAvailableStreams(message);
        break;
      case "available-tabs-updated":
        this.handleAvailableTabsUpdated(message);
        break;
      case "target-tab-available":
        this.handleTargetTabAvailable(message);
        break;
      case "target-tab-unavailable":
        this.handleTargetTabUnavailable(message);
        break;
      case "target-tab-updated":
        this.handleTargetTabUpdated(message);
        break;
      case "stream-ready":
        this.handleStreamReady(message);
        break;
      case "stream-ended":
        this.handleStreamEnded(message);
        break;
      case "webrtc-offer":
        this.handleWebRTCOffer(message);
        break;
      case "webrtc-answer":
        this.handleWebRTCAnswer(message);
        break;
      case "webrtc-ice-candidate":
        this.handleWebRTCIceCandidate(message);
        break;
      case "error":
        this.log("error", `Server error: ${message.message}`);
        break;
      default:
        this.log("warn", `Unknown message type: ${message.type}`);
    }
  }

  handleWelcome(message) {
    this.clientId = message.clientId;
    this.log("success", `Registered with client ID: ${this.clientId}`);

    // Register as web client
    this.sendMessage({
      type: "register-web-client",
      metadata: {
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
      },
    });
  }

  handleAvailableStreams(message) {
    this.log("info", `Received ${message.targetTabs.length} available tabs`);

    // Update available tabs
    this.availableTabs.clear();
    message.targetTabs.forEach((tab) => {
      this.availableTabs.set(tab.tabId, tab);
    });

    this.updateTabsGrid();
  }

  handleAvailableTabsUpdated(message) {
    this.log("info", `Available tabs updated: ${message.tabs.length} tabs`);

    // Update available tabs for single-stream mode
    this.availableTabs.clear();
    message.tabs.forEach((tab) => {
      this.availableTabs.set(tab.tabId, tab);
    });

    this.updateTabsGrid();
  }

  handleTargetTabAvailable(message) {
    this.log("info", `New tab available: ${message.title}`);
    this.availableTabs.set(message.tabId, message);
    this.updateTabsGrid();
  }

  handleTargetTabUnavailable(message) {
    this.log("warn", `Tab unavailable: ${message.tabId}`);
    this.availableTabs.delete(message.tabId);
    this.updateTabsGrid();

    // Stop any active streams for this tab
    if (this.currentStream) {
      this.stopCurrentStream();
    }
  }

  handleTargetTabUpdated(message) {
    this.log("info", `Tab updated: ${message.title}`);
    if (this.availableTabs.has(message.tabId)) {
      const existingTab = this.availableTabs.get(message.tabId);
      this.availableTabs.set(message.tabId, { ...existingTab, ...message });
      this.updateTabsGrid();
    }
  }

  handleStreamReady(message) {
    this.log("success", `Stream ready for tab: ${message.tabId}`);

    // For single-stream mode, just update the stream info
    const tabInfo = this.availableTabs.get(message.tabId);
    if (tabInfo) {
      this.updateStreamInfo(
        `🟢 Streaming from: ${tabInfo.title || "Unknown Tab"}`
      );
      this.log("info", `Single stream ready for tab: ${message.tabId}`);
    } else {
      this.log("warn", `No tab info found for tabId: ${message.tabId}`);
    }
  }

  handleStreamEnded(message) {
    this.log("warn", `Stream ended for tab: ${message.tabId}`);
    this.stopCurrentStream();
  }

  async handleWebRTCOffer(message) {
    this.log("info", `Received WebRTC offer for tab: ${message.tabId}`);

    try {
      // For single-stream mode, stop any existing stream
      if (this.currentStream) {
        this.currentStream.peerConnection.close();
        this.currentStream = null;
      }

      // Create peer connection
      const peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Setup data channel for user events
      peerConnection.ondatachannel = (event) => {
        this.dataChannel = event.channel;
        this.setupDataChannelHandlers();
      };

      // Setup event handlers
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          console.log("candidate");
          this.sendMessage({
            type: "webrtc-ice-candidate-from-web-client",
            candidate: event.candidate,
            tabId: message.tabId,
          });
        }
      };

      peerConnection.ontrack = (event) => {
        this.log("success", `Received media track for tab: ${message.tabId}`);
        console.log("[WebClient] ontrack event:", event);

        const [stream] = event.streams;
        console.log("[WebClient] stream:", stream);

        // Display stream in single video element
        this.displaySingleStream(stream, message.tabId);
      };

      peerConnection.onconnectionstatechange = () => {
        this.log("info", `Connection state: ${peerConnection.connectionState}`);
        if (peerConnection.connectionState === "failed") {
          this.log(
            "error",
            `WebRTC connection failed for tab: ${message.tabId}`
          );
          this.stopCurrentStream();
        }
      };

      // Set remote description
      await peerConnection.setRemoteDescription(message.offer);

      // Create answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // Send answer
      console.log("sending answer");
      this.sendMessage({
        type: "webrtc-answer-from-web-client",
        answer: answer,
        tabId: message.tabId,
      });

      // Store as current stream
      this.currentStream = {
        peerConnection,
        stream: null,
        tabId: message.tabId,
      };
    } catch (error) {
      this.log("error", `Failed to handle WebRTC offer: ${error.message}`);
    }
  }

  async handleWebRTCAnswer(message) {
    this.log("info", `Received WebRTC answer for tab: ${message.tabId}`);

    const streamData = this.currentStream;
    if (streamData && streamData.peerConnection) {
      try {
        await streamData.peerConnection.setRemoteDescription(message.answer);
      } catch (error) {
        this.log("error", `Failed to set remote description: ${error.message}`);
      }
    }
  }

  async handleWebRTCIceCandidate(message) {
    const streamData = this.currentStream;
    if (streamData && streamData.peerConnection) {
      try {
        await streamData.peerConnection.addIceCandidate(
          new RTCIceCandidate(message.candidate)
        );
      } catch (error) {
        this.log("error", `Failed to add ICE candidate: ${error.message}`);
      }
    }
  }

  displayStream(tabId, mediaStream, tabInfo) {
    this.log("success", `Displaying stream for tab: ${tabId}`);
    console.log("[WebClient] displayStream called with:", {
      tabId,
      mediaStream,
      tabInfo,
    });
    console.log(
      "[WebClient] mediaStream.getTracks():",
      mediaStream.getTracks()
    );

    // Update stream data
    const streamData = this.currentStream;
    if (streamData) {
      streamData.stream = mediaStream;
    }

    // Only create UI if it doesn't already exist
    const existingContainer = document.getElementById(`stream-${tabId}`);
    if (!existingContainer) {
      this.addStreamToUI(tabId, mediaStream, tabInfo);
    } else {
      // Update existing video element with new stream
      const video = existingContainer.querySelector(".stream-video");
      if (video) {
        video.srcObject = mediaStream;
      }
    }
  }

  addStreamToUI(tabId, mediaStream, tabInfo) {
    // Remove empty state if present
    const emptyState = this.streamsGrid.querySelector(".empty-state");
    if (emptyState) {
      emptyState.remove();
    }

    const streamContainer = document.createElement("div");
    streamContainer.className = "stream-container";
    streamContainer.id = `stream-${tabId}`;

    streamContainer.innerHTML = `
      <div class="stream-header">
        📺 ${tabInfo.title || "Unknown Tab"}
      </div>
      <div class="stream-content">
        <video class="stream-video" autoplay muted playsinline></video>
      </div>
      <div class="stream-controls">
        <div class="stream-info">
          <div>${tabInfo.url || "Unknown URL"}</div>
          <div>Tab ID: ${tabId.substring(0, 8)}...</div>
        </div>
        <button class="btn btn-secondary btn-small" onclick="client.stopStream('${tabId}')">
          Stop Stream
        </button>
      </div>
    `;

    const video = streamContainer.querySelector(".stream-video");
    console.log("[WebClient] Setting video.srcObject:", { video, mediaStream });
    video.srcObject = mediaStream;

    // Add event listeners to video element for debugging
    video.onloadedmetadata = () => {
      console.log(
        "[WebClient] Video metadata loaded:",
        video.videoWidth,
        "x",
        video.videoHeight
      );
    };
    video.onplay = () => {
      console.log("[WebClient] Video started playing");
    };
    video.onerror = (error) => {
      console.error("[WebClient] Video error:", error);
    };

    this.streamsGrid.appendChild(streamContainer);
  }

  removeStreamFromUI(tabId) {
    const streamElement = document.getElementById(`stream-${tabId}`);
    if (streamElement) {
      streamElement.remove();
    }

    // Add empty state if no streams left
    if (this.streamsGrid.children.length === 0) {
      this.streamsGrid.innerHTML = `
        <div class="empty-state">
          <h3>No active streams</h3>
          <p>Click "Start Stream" on any available tab to begin streaming</p>
        </div>
      `;
    }
  }

  updateTabsGrid() {
    if (this.availableTabs.size === 0) {
      this.tabsGrid.innerHTML = `
        <div class="empty-state">
          <h3>No target tabs available</h3>
          <p>Connect to the signaling server to see available tabs</p>
        </div>
      `;
      return;
    }

    this.tabsGrid.innerHTML = "";

    for (const [tabId, tabInfo] of this.availableTabs) {
      const isCurrentStream = this.currentTabId === tabId;

      const tabCard = document.createElement("div");
      tabCard.className = `tab-card ${isCurrentStream ? "streaming" : ""}`;

      // Make tab clickable for single-stream mode
      if (!isCurrentStream) {
        tabCard.style.cursor = "pointer";
        tabCard.addEventListener("click", () => this.requestStream(tabId));
      }

      tabCard.innerHTML = `
        <div class="tab-title">${tabInfo.title || "Unknown Tab"}</div>
        <div class="tab-url">${tabInfo.url || "Unknown URL"}</div>
        <div class="tab-status">
          ${isCurrentStream ? "🟢 Currently Streaming" : "🔵 Click to Stream"}
        </div>
      `;

      this.tabsGrid.appendChild(tabCard);
    }
  }

  /**
   * Request single stream for a tab
   */
  requestStream(tabId) {
    const tabInfo = this.availableTabs.get(tabId);
    if (!tabInfo) {
      this.log("error", `Tab not found: ${tabId}`);
      return;
    }

    this.log("info", `Requesting stream for: ${tabInfo.title}`);

    // Send request to signaling server
    this.sendMessage({
      type: "request-stream",
      tabId: tabId,
    });

    // Update current tab
    this.currentTabId = tabId;
    this.updateTabsGrid();
  }

  /**
   * Update stream info display
   */
  updateStreamInfo(message) {
    if (this.streamInfo) {
      this.streamInfo.innerHTML = `<p>${message}</p>`;
    }
  }

  /**
   * Display single stream in video element
   */
  displaySingleStream(stream, tabId) {
    if (this.currentStreamVideo) {
      this.currentStreamVideo.srcObject = stream;
      this.log("success", `Stream displayed for tab: ${tabId}`);

      // Update stream info
      const tabInfo = this.availableTabs.get(this.currentTabId);
      if (tabInfo) {
        this.updateStreamInfo(
          `🟢 Streaming from: ${tabInfo.title || "Unknown Tab"}`
        );
      }

      // Add click event listener for user event replay
      this.setupVideoClickHandler(tabId);
    }
  }

  /**
   * Stop current stream
   */
  stopCurrentStream() {
    return;
    if (this.currentStream) {
      this.currentStream.peerConnection.close();
      this.currentStream = null;
    }

    if (this.currentStreamVideo) {
      this.currentStreamVideo.srcObject = null;
    }

    this.currentTabId = null;
    this.updateStreamInfo(
      "No stream active. Click on a tab above to start streaming."
    );
    this.updateTabsGrid();
  }

  clearStreams() {
    // Stop current stream
    this.stopCurrentStream();
  }

  updateStatus(status, text) {
    this.statusText.textContent = text;
    this.statusIndicator.className = `status-indicator ${
      status === "connected" ? "connected" : ""
    }`;
  }

  updateUI() {
    this.connectBtn.disabled = this.isConnected;
    this.disconnectBtn.disabled = !this.isConnected;
  }

  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    } else {
      this.log("warn", "Cannot send message - not connected");
    }
  }

  log(level, message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement("div");
    logEntry.className = "log-entry";
    logEntry.innerHTML = `
      <span class="log-timestamp">[${timestamp}]</span>
      <span class="log-level-${level}">[${level.toUpperCase()}]</span>
      ${message}
    `;

    this.logsContainer.appendChild(logEntry);
    this.logsContainer.scrollTop = this.logsContainer.scrollHeight;

    // Keep only last 100 log entries
    while (this.logsContainer.children.length > 100) {
      this.logsContainer.removeChild(this.logsContainer.firstChild);
    }

    console.log(`[${level.toUpperCase()}] ${message}`);
  }

  generateId() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  /**
   * Setup data channel handlers for user event transmission
   */
  setupDataChannelHandlers() {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      console.log("[WebClient] Data channel opened for user events");
      this.log("success", "User event replay enabled");
    };

    this.dataChannel.onclose = () => {
      console.log("[WebClient] Data channel closed");
      this.log("info", "User event replay disabled");
    };

    this.dataChannel.onerror = (error) => {
      console.error("[WebClient] Data channel error:", error);
      this.log("error", "User event replay error");
    };

    this.dataChannel.onmessage = (event) => {
      console.log("[WebClient] Received data channel message:", event.data);
    };
  }

  /**
   * Setup click event handler on video element
   */
  setupVideoClickHandler(tabId) {
    if (!this.currentStreamVideo) return;

    // Remove existing click handler
    this.currentStreamVideo.removeEventListener(
      "click",
      this.videoClickHandler
    );

    // Create new click handler
    this.videoClickHandler = (event) => {
      this.handleVideoClick(event, tabId);
    };

    // Add click handler
    this.currentStreamVideo.addEventListener("click", this.videoClickHandler);
    console.log("[WebClient] Video click handler setup for tab:", tabId);
  }

  /**
   * Handle click events on video element
   */
  handleVideoClick(event, tabId) {
    if (!this.dataChannel || this.dataChannel.readyState !== "open") {
      console.warn("[WebClient] Data channel not available for user events");
      return;
    }

    // Get click coordinates relative to video element
    const rect = this.currentStreamVideo.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Get video dimensions
    const videoWidth = this.currentStreamVideo.videoWidth;
    const videoHeight = this.currentStreamVideo.videoHeight;
    const displayWidth = rect.width;
    const displayHeight = rect.height;

    // Calculate normalized coordinates (0-1)
    const normalizedX = x / displayWidth;
    const normalizedY = y / displayHeight;

    // Create user event message
    const userEvent = {
      type: "user-event",
      eventType: "click",
      x: normalizedX,
      y: normalizedY,
      tabId: tabId,
      timestamp: Date.now(),
      videoWidth: videoWidth,
      videoHeight: videoHeight,
      displayWidth: displayWidth,
      displayHeight: displayHeight,
    };

    // Send through data channel
    try {
      this.dataChannel.send(JSON.stringify(userEvent));
      console.log("[WebClient] Sent user event:", userEvent);
      this.log(
        "info",
        `Click sent: (${Math.round(normalizedX * 100)}%, ${Math.round(
          normalizedY * 100
        )}%)`
      );
    } catch (error) {
      console.error("[WebClient] Failed to send user event:", error);
      this.log("error", "Failed to send click event");
    }
  }
}

// Initialize the client
const client = new StreamingClient();
