#!/usr/bin/env node

/**
 * Puppeteer Automation Script for Tab Screen Share Extension
 *
 * This script automates the testing of multi-stream functionality by:
 * 1. Launching Chrome with the extension loaded
 * 2. Opening multiple test tabs
 * 3. Programmatically triggering screen sharing for each tab
 * 4. Verifying that the web client receives all streams
 *
 * Usage: node automation-script.js
 */

import puppeteer from "puppeteer";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class TabScreenShareAutomation {
  constructor() {
    this.browser = null;
    this.extensionPage = null;
    this.testTabs = [];
    this.extensionId = null;

    // Configuration
    this.config = {
      extensionPath: path.join(__dirname, "tab-screen-share-extension"),
      signalingServerUrl: "ws://localhost:3001",
      webClientUrl: "http://localhost:3000",
      testPageUrl: null, // Will be set to local file URL
      numberOfStreams: 1, // Start with 1 stream to test stability
      delayBetweenActions: 3000, // 3 seconds - more time between actions
    };
  }

  async initialize() {
    console.log("🚀 Starting Tab Screen Share Automation...");

    // Set test page URL to local file
    this.config.testPageUrl = `file://${path.join(
      this.config.extensionPath,
      "test-page.html"
    )}`;

    console.log(`📁 Extension path: ${this.config.extensionPath}`);
    console.log(`🌐 Test page URL: ${this.config.testPageUrl}`);

    // Launch Chrome with extension
    await this.launchBrowser();

    // Get extension ID
    await this.getExtensionId();

    console.log(`🔧 Extension loaded with ID: ${this.extensionId}`);
  }

  async launchBrowser() {
    console.log("🌐 Launching Chrome browser with extension...");

    this.browser = await puppeteer.launch({
      headless: false, // Keep visible for testing
      devtools: false,
      args: [
        `--disable-extensions-except=${this.config.extensionPath}`,
        `--load-extension=${this.config.extensionPath}`,
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-web-security",
        "--allow-running-insecure-content",
        "--autoplay-policy=no-user-gesture-required",
        "--use-fake-ui-for-media-stream", // Auto-approve media permissions
        "--use-fake-device-for-media-stream",
        "--disable-features=VizDisplayCompositor", // Help with extension loading
      ],
      defaultViewport: null,
    });

    console.log("✅ Browser launched successfully");

    // Wait a bit for extensions to load
    await this.delay(3000);

    // Debug: List all targets
    const allTargets = await this.browser.targets();
    console.log(`🔍 All browser targets (${allTargets.length}):`);
    for (const target of allTargets) {
      console.log(`  - ${target.type()}: ${target.url()}`);
    }
  }

  async getExtensionId() {
    // Try multiple approaches to get the extension ID
    console.log("🔍 Detecting extension ID...");

    // Approach 1: Get extension targets directly from browser
    const targets = await this.browser.targets();
    const extensionTargets = targets.filter(
      (target) =>
        target.type() === "background_page" ||
        target.url().startsWith("chrome-extension://")
    );

    console.log(`Found ${extensionTargets.length} extension targets`);

    for (const target of extensionTargets) {
      const url = target.url();
      console.log(`Checking target: ${url}`);

      const match = url.match(/chrome-extension:\/\/([a-z]+)/);
      if (match) {
        const potentialId = match[1];

        // Test if this extension has our popup
        try {
          const testPage = await this.browser.newPage();
          const testUrl = `chrome-extension://${potentialId}/popup.html`;
          console.log(`Testing extension URL: ${testUrl}`);

          await testPage.goto(testUrl, { timeout: 5000 });

          // Check if this looks like our extension
          const hasOurElements = await testPage.evaluate(() => {
            return document.querySelector("#startCaptureBtn") !== null;
          });

          await testPage.close();

          if (hasOurElements) {
            this.extensionId = potentialId;
            console.log(`✅ Found extension ID: ${this.extensionId}`);
            return;
          }
        } catch (e) {
          console.log(`  ❌ Failed to access ${potentialId}: ${e.message}`);
        }
      }
    }

    if (!this.extensionId) {
      throw new Error(
        "Extension not found. Make sure the extension is loaded correctly and has the right popup.html file."
      );
    }
  }

  async createTestTabs() {
    console.log(`📑 Creating ${this.config.numberOfStreams} test tabs...`);

    for (let i = 0; i < this.config.numberOfStreams; i++) {
      const page = await this.browser.newPage();

      // Navigate to test page with unique content
      await page.goto(this.config.testPageUrl);

      // Customize each tab to make them visually distinct
      await page.evaluate((tabNumber) => {
        document.title = `Test Tab ${tabNumber + 1}`;
        const container = document.querySelector(".container");
        if (container) {
          const header = container.querySelector("h1");
          if (header) {
            header.textContent = `Test Tab ${
              tabNumber + 1
            } - Screen Share Test`;
            header.style.color =
              ["#ff6b6b", "#4ecdc4", "#45b7d1"][tabNumber] || "#333";
          }
        }
      }, i);

      this.testTabs.push({
        page,
        tabNumber: i + 1,
        isCapturing: false,
        isStreaming: false,
      });

      console.log(`✅ Created test tab ${i + 1}`);
      await this.delay(500);
    }
  }

  async startScreenSharing() {
    console.log("🎥 Starting screen sharing for all tabs...");

    for (const tab of this.testTabs) {
      console.log(`📹 Starting capture for tab ${tab.tabNumber}...`);

      // Focus the test tab and make sure it's the active tab
      await tab.page.bringToFront();
      await this.delay(1000); // Give more time for tab to become active

      // Use popup method but ensure the test tab stays active
      await this.startCaptureViaPopup(tab);

      // Wait between starting each stream
      await this.delay(this.config.delayBetweenActions);
    }

    console.log("🎉 All tabs are now capturing and streaming!");
  }

  async startCaptureViaPopup(tab) {
    // Focus the test tab first and ensure it's really active
    await tab.page.bringToFront();
    await this.delay(1000);

    console.log(`  📍 Test tab ${tab.tabNumber} is now active`);

    try {
      // Method 1: Try to trigger extension popup via CDP
      await this.triggerExtensionPopupViaCDP(tab);
    } catch (error) {
      console.log(`  ⚠️  CDP method failed: ${error.message}`);
      console.log(`  🔄 Falling back to direct popup access...`);

      // Method 2: Fallback to direct popup access
      await this.triggerExtensionPopupDirect(tab);
    }
  }

  async triggerExtensionPopupViaCDP(tab) {
    // Use Chrome DevTools Protocol to trigger the extension action
    const client = await tab.page.target().createCDPSession();

    // Get the extension's action (popup)
    await client.send("Runtime.enable");

    // Trigger the extension action - this simulates clicking the extension icon
    try {
      // This is the proper way to trigger an extension popup
      await client.send("Runtime.evaluate", {
        expression: `
          chrome.action.openPopup ? chrome.action.openPopup() :
          chrome.browserAction.openPopup ? chrome.browserAction.openPopup() :
          console.log('No popup method available')
        `,
        awaitPromise: true,
      });

      console.log(`  ✅ Triggered extension popup via CDP`);

      // Wait for popup to appear and interact with it
      await this.interactWithExtensionPopup(tab);
    } catch (error) {
      throw new Error(`CDP popup trigger failed: ${error.message}`);
    } finally {
      await client.detach();
    }
  }

  async triggerExtensionPopupDirect(tab) {
    // Fallback: Open extension popup in new page but keep test tab active
    const extensionUrl = `chrome-extension://${this.extensionId}/popup.html`;
    const popupPage = await this.browser.newPage();

    // Set a smaller viewport for the popup to simulate real popup behavior
    await popupPage.setViewport({ width: 400, height: 600 });

    await popupPage.goto(extensionUrl);
    console.log(`  🔧 Opened extension popup directly`);

    // Immediately refocus the test tab to ensure it stays active
    await tab.page.bringToFront();
    await this.delay(500);

    // Interact with the popup
    await this.interactWithPopupPage(popupPage, tab);

    await popupPage.close();
  }

  async interactWithExtensionPopup(tab) {
    // When using CDP, the popup appears as an overlay
    // We need to find it among the browser targets
    await this.delay(1000);

    // const targets = await this.browser.targets();
    // const popupTarget = targets.find((target) =>
    //   target.url().includes(`chrome-extension://${this.extensionId}/popup.html`)
    // );

    // if (!popupTarget) {
    //   throw new Error("Extension popup not found after CDP trigger");
    // }

    // const popupPage = await popupTarget.page();
    // if (!popupPage) {
    //   throw new Error("Could not access popup page");
    // }

    console.log(`  🎯 Found extension popup via CDP`);
    // await this.interactWithPopupPage(popupPage, tab);
  }

  async interactWithPopupPage(popupPage, tab) {
    // Wait for popup to fully load
    await popupPage.waitForSelector("#startCaptureBtn", { timeout: 5000 });
    await this.delay(500);

    // Ensure test tab is active before clicking
    await tab.page.bringToFront();
    await this.delay(500);

    // Click start capture
    await popupPage.click("#startCaptureBtn");
    console.log(`  ✅ Clicked start capture for tab ${tab.tabNumber}`);

    // Wait for capture to initialize
    await this.delay(3000);

    // Ensure test tab is still active before starting WebRTC
    await tab.page.bringToFront();
    await this.delay(500);

    // Start WebRTC streaming
    await popupPage.waitForSelector("#startWebRTCBtn", { timeout: 5000 });
    await popupPage.click("#startWebRTCBtn");
    console.log(`  ✅ Started WebRTC streaming for tab ${tab.tabNumber}`);

    // Mark as capturing and streaming
    tab.isCapturing = true;
    tab.isStreaming = true;

    // Wait a bit to let WebRTC initialize
    await this.delay(2000);
  }

  async openWebClient() {
    console.log("🌐 Opening web client...");

    const clientPage = await this.browser.newPage();
    await clientPage.goto(this.config.webClientUrl);

    // Wait for page to load
    await clientPage.waitForSelector("#connectBtn", { timeout: 10000 });

    // Connect to signaling server
    await clientPage.click("#connectBtn");
    console.log("✅ Connected to signaling server");

    // Wait longer for streams to appear and check multiple times
    console.log("⏳ Waiting for streams to appear...");
    let streamCount = 0;
    let attempts = 0;
    const maxAttempts = 10;

    while (
      attempts < maxAttempts &&
      streamCount < this.config.numberOfStreams
    ) {
      await this.delay(2000);
      attempts++;

      streamCount = await clientPage.evaluate(() => {
        const streamElements = document.querySelectorAll(".stream-item");
        return streamElements.length;
      });

      console.log(`  Attempt ${attempts}: ${streamCount} stream(s) found`);

      if (streamCount > 0) {
        // If we found some streams, wait a bit more for others
        await this.delay(3000);
        streamCount = await clientPage.evaluate(() => {
          const streamElements = document.querySelectorAll(".stream-item");
          return streamElements.length;
        });
        break;
      }
    }

    console.log(`📊 Web client is displaying ${streamCount} stream(s)`);

    if (streamCount === this.config.numberOfStreams) {
      console.log("🎉 SUCCESS: All streams are displayed correctly!");
    } else {
      console.log(
        `⚠️  WARNING: Expected ${this.config.numberOfStreams} streams, but got ${streamCount}`
      );

      // Debug: Check what's in the logs
      const logs = await clientPage.evaluate(() => {
        const logContainer = document.getElementById("logContainer");
        return logContainer ? logContainer.textContent : "No logs found";
      });
      console.log("📋 Client logs:", logs.slice(-500)); // Last 500 chars
    }

    return { clientPage, streamCount };
  }

  async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async cleanup() {
    console.log("🧹 Cleaning up...");

    if (this.browser) {
      await this.browser.close();
      console.log("✅ Browser closed");
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.createTestTabs();
      await this.startScreenSharing();

      const { clientPage, streamCount } = await this.openWebClient();

      console.log("\n📋 Test Results:");
      console.log(`  • Test tabs created: ${this.testTabs.length}`);
      console.log(
        `  • Streams started: ${
          this.testTabs.filter((t) => t.isStreaming).length
        }`
      );
      console.log(`  • Streams displayed in web client: ${streamCount}`);
      console.log(
        `  • Test status: ${
          streamCount === this.config.numberOfStreams
            ? "PASSED ✅"
            : "FAILED ❌"
        }`
      );

      console.log(
        "\n🎯 Automation complete! Browser will remain open for manual inspection."
      );
      console.log("   Press Ctrl+C to close the browser and exit.");

      // Keep the script running so browser stays open
      process.on("SIGINT", async () => {
        console.log("\n👋 Shutting down...");
        await this.cleanup();
        process.exit(0);
      });

      // Keep alive
      await new Promise(() => {});
    } catch (error) {
      console.error("❌ Automation failed:", error.message);
      console.error(error.stack);
      await this.cleanup();
      process.exit(1);
    }
  }
}

// Run the automation
const automation = new TabScreenShareAutomation();
automation.run();
