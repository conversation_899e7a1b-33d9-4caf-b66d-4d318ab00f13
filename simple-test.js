#!/usr/bin/env node

/**
 * Simple CDP Script - Test Extension Screen Sharing
 *
 * Prerequisites:
 * 1. Start Chrome with extension loaded:
 *    ./start-chrome.sh
 *
 * 2. Start signaling server:
 *    cd signaling-server && node server.js
 *
 * 3. Run this script:
 *    node simple-test.js
 */

import { CDP } from "./simple-cdp.js";
import WebSocket from "ws";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function simpleTest() {
  console.log("🧪 Starting simple CDP extension test...");

  let sessionId = null;

  try {
    const extensionPath = path.join(__dirname, "tab-screen-share-extension");
    const testPageUrl = `file://${path.join(extensionPath, "test-page.html")}`;

    // Create CDP connection to remote browser
    const wsEndpoint = "ws://localhost:9222";
    const cdpInstance = new CDP({ webSocketDebuggerUrl: wsEndpoint });

    // For remote browsers, we need to attach to a target and get sessionId
    console.log("� Getting list of targets...");
    const targets = await cdpInstance.Target.getTargets();
    console.log("🎯 Available targets:", targets.targetInfos.length);

    // Find a page target or create one
    let pageTarget = targets.targetInfos.find(
      (target) => target.type === "page"
    );

    if (!pageTarget) {
      console.log("📍 Creating new page target...");
      const createResult = await cdpInstance.Target.createTarget({
        url: testPageUrl
      });
      pageTarget = { targetId: createResult.targetId };
    }

    console.log("📍 Found/created page target:", pageTarget.targetId);

    // Attach to the target
    const attachResult = await cdpInstance.Target.attachToTarget({
      targetId: pageTarget.targetId,
      flatten: true,
    });

    sessionId = attachResult.sessionId;
    console.log("✅ Attached to target with sessionId:", sessionId);

    // Enable necessary domains (with sessionId for remote browsers)
    await cdpInstance.Runtime.enable(null, sessionId);
    console.log("✅ Runtime domain enabled");

    await cdpInstance.Page.enable(null, sessionId);
    console.log("✅ Page domain enabled");

    // Navigate to our test page
    console.log(`📍 Navigating to: ${testPageUrl}`);
    await cdpInstance.Page.navigate({ url: testPageUrl }, sessionId);

    // Wait for page to load
    console.log("⏳ Waiting for page to load...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Get page title to confirm we're on the right page
    const titleResult = await cdpInstance.Runtime.evaluate(
      {
        expression: "document.title",
        returnByValue: true,
      },
      sessionId
    );
    console.log("📄 Page title:", titleResult.result.value);

  // Find extension ID by listing all targets
  console.log("🔍 Finding extension...");

  const targetsResponse = await fetch("http://localhost:9222/json");
  const targets = await targetsResponse.json();

  let extensionId = null;
  for (const target of targets) {
    if (target.url && target.url.startsWith("chrome-extension://")) {
      const match = target.url.match(/chrome-extension:\/\/([a-z]+)/);
      if (match) {
        const potentialId = match[1];

        // Test if this extension has our popup
        try {
          const popupTargetInfo = await createTarget(
            `chrome-extension://${potentialId}/popup.html`
          );
          const popupTab = new CDP(popupTargetInfo);
          await popupTab.Runtime.enable();
          await popupTab.Page.enable();

          // Check if popup has our button
          const hasButton = await popupTab.Runtime.evaluate({
            expression: 'document.querySelector("#startCaptureBtn") !== null',
            returnByValue: true,
          });

          if (hasButton.result.value) {
            extensionId = potentialId;
            console.log(`🔧 Found extension: ${extensionId}`);

            // Now interact with the popup
            console.log("🎬 Clicking start capture...");

            // Click start capture button
            await popupTab.Runtime.evaluate({
              expression: 'document.querySelector("#startCaptureBtn").click()',
              returnByValue: true,
            });

            // Wait for capture to initialize
            await new Promise((resolve) => setTimeout(resolve, 3000));

            // Check if WebRTC button is available
            const hasWebRTCButton = await popupTab.Runtime.evaluate({
              expression: 'document.querySelector("#startWebRTCBtn") !== null',
              returnByValue: true,
            });

            if (hasWebRTCButton.result.value) {
              console.log("✅ WebRTC button available");

              // Click WebRTC button
              console.log("� Clicking start WebRTC...");
              await popupTab.Runtime.evaluate({
                expression: 'document.querySelector("#startWebRTCBtn").click()',
                returnByValue: true,
              });

              console.log("✅ WebRTC started");

              // Wait a bit to see results
              await new Promise((resolve) => setTimeout(resolve, 5000));
            } else {
              console.log("❌ WebRTC button not available");
            }

            break;
          }
        } catch (e) {
          // Not our extension or failed to load popup
          continue;
        }
      }
    }
  }

  if (!extensionId) {
    console.error("❌ Extension not found");
    console.error("   Make sure Chrome is running with the extension loaded:");
    console.error(
      "   chrome --remote-debugging-port=9222 --load-extension=./tab-screen-share-extension"
    );
    return;
  }

  console.log("🎯 Test complete!");
  console.log("   Check the signaling server logs for stream notifications.");
  console.log("   Open http://localhost:3000 to see if streams appear.");
}

simpleTest().catch(console.error);
